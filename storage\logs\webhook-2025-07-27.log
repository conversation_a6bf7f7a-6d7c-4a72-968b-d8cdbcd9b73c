[2025-07-27 10:43:07] local.INFO: Starting webhook dispatch for action: booking.event_created {"timestamp":"2025-07-27T10:43:06.972037Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"entity_type":null,"entity_id":null,"status":"dispatching"} 
[2025-07-27 10:43:12] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2031 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-27T10:43:12.932126Z","source":"crm_webhook_system","action":"booking.event_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":5898.0,"user_id":84,"entity_id":6,"entity_type":null,"request_payload":{"action":"booking.event_created","timestamp":"2025-07-27T10:43:07.034627Z","data":{"id":6,"title":"Tabnine AI Bot","start_date":"2025-07-27 10:42:26","end_date":"2026-07-27 10:42:26","duration":60,"booking_per_slot":1,"minimum_notice":30,"description":"Tabnine AI Bot
Your AI coding assistant for faster, smarter development.

Tabnine is an AI-powered code completion tool designed to supercharge developer productivity. Using advanced machine learning models trained on open-source code, Tabnine provides intelligent code suggestions, entire line completions, and context-aware snippets across multiple programming languages and IDEs.

🔑 Key Features:
AI Autocomplete: Predicts and completes code as you type, reducing boilerplate writing.

Supports Multiple Languages: Works with JavaScript, Python, PHP, Java, C++, and more.

IDE Integration: Seamlessly integrates with VS Code, JetBrains IDEs, Sublime Text, and others.

Team Training Mode: Learns from your team’s codebase for private, project-specific suggestions.

Privacy-Focused: Offers local AI models and doesn’t send your code to the cloud (optional).

✅ Best For:
Individual developers, teams, and enterprises are looking to streamline their workflow with AI-assisted coding.","location":"zoom","meet_link":"https://meet.google.com/uvo-oxip-ijn","physical_address":"Siliguri Matighara Krishnakanta Rode.","custom_fields":[{"type":"whatsapp_number","label":"Whatsapp Number"},{"type":"lead_value","label":"Lead Value"}],"date_override":"[\"2025-07-30T04:30\",\"2025-07-30T07:00\"]","created_by":84,"slots_created":2928,"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2031 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-27 10:43:12] local.WARNING: Webhook dispatch completed for action: booking.event_created. Success: 0, Failed: 1 {"timestamp":"2025-07-27T10:43:12.934013Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2031 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-07-27 16:36:56] local.INFO: Starting webhook dispatch for action: booking.event_created {"timestamp":"2025-07-27T16:36:56.226173Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"entity_type":null,"entity_id":null,"status":"dispatching"} 
[2025-07-27 16:36:58] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2032 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-27T16:36:58.423388Z","source":"crm_webhook_system","action":"booking.event_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2111.0,"user_id":84,"entity_id":3,"entity_type":null,"request_payload":{"action":"booking.event_created","timestamp":"2025-07-27T16:36:56.311992Z","data":{"id":3,"title":"AI Bot Flow Builder","start_date":"2025-07-27 16:36:33","end_date":"2026-07-27 16:36:33","duration":60,"booking_per_slot":1,"minimum_notice":45,"description":"OK","location":"phone","meet_link":"8617555736","physical_address":null,"custom_fields":null,"date_override":null,"created_by":84,"slots_created":2928,"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2032 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-27 16:36:58] local.WARNING: Webhook dispatch completed for action: booking.event_created. Success: 0, Failed: 1 {"timestamp":"2025-07-27T16:36:58.424715Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2032 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-07-27 16:37:46] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-27T16:37:46.201453Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":4,"status":"dispatching"} 
[2025-07-27 16:37:48] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2060 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-27T16:37:48.369031Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2140.0,"user_id":84,"entity_id":4,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-27T16:37:46.228614Z","data":{"event_id":3,"name":"Parichay Singha","email":"<EMAIL>","phone":"08617555736","date":"2025-07-29","time":"13:30","selected_location":{"type":"phone","value":"8617555736","display":"Phone call"},"custom_fields":[],"custom_fields_value":[],"updated_at":"2025-07-27T16:37:46.000000Z","created_at":"2025-07-27T16:37:46.000000Z","id":4,"form_data":{"name":"Parichay Singha","email":"<EMAIL>","phone":"08617555736","date":"2025-07-29","time":"13:30","selected_location":{"type":"phone","value":"8617555736","display":"Phone call"},"custom_fields":[],"custom_fields_value":[],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2060 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-27 16:37:48] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 1 {"timestamp":"2025-07-27T16:37:48.370360Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2060 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
