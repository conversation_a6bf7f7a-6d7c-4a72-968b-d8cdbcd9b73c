# Custom Fields Integration for Calendar Events

## Overview
This implementation integrates the existing Custom Fields system with Calendar Events, allowing users to configure custom fields in the settings and use them in event booking forms.

## How It Works

### 1. Create Custom Fields in Settings
1. Navigate to **Settings > Custom Fields**
2. Click **Create Custom Field**
3. Fill in the details:
   - **Name**: The label that will appear in forms
   - **Type**: Choose from text, email, number, date, textarea, select, radio, checkbox, etc.
   - **Module**: Select **"Calendar Events"**
   - **Options**: For select/radio/checkbox types, add the available options
   - **Required**: Check if the field should be mandatory

### 2. Add Custom Fields to Events
1. Go to **Smart Scheduler > Create/Edit**
2. Click **Create New Event**
3. In the **Custom Fields** section:
   - Select a field type from the dropdown (populated from your settings)
   - Click **Add Field** to include it in the event
   - Repeat to add multiple custom fields
   - Remove fields using the trash icon if needed

### 3. Public Booking Form
When users access the public booking link:
- Custom fields will automatically appear in the booking form
- Field types will render appropriately (text inputs, dropdowns, checkboxes, etc.)
- Required fields will be marked with a red asterisk (*)
- Users must fill out all required fields before booking

### 4. Booking Confirmation
After successful booking:
- Custom field data is saved with the booking
- Data appears in the booking confirmation page
- Admin can view custom field responses in the bookings list

## Supported Field Types

| Type | Description | Input Element |
|------|-------------|---------------|
| text | Single line text | `<input type="text">` |
| email | Email address | `<input type="email">` |
| number | Numeric input | `<input type="number">` |
| date | Date picker | `<input type="date">` |
| datetime | Date and time picker | `<input type="datetime-local">` |
| textarea | Multi-line text | `<textarea>` |
| select | Dropdown menu | `<select>` |
| radio | Radio button group | Radio buttons |
| checkbox | Checkbox group | Checkboxes |
| multiselect | Multi-select dropdown | `<select multiple>` |
| file | File upload | `<input type="file">` |
| file_multiple | Multiple file upload | `<input type="file" multiple>` |
| color | Color picker | `<input type="color">` |
| link | URL input | `<input type="url">` |

## Database Structure

### Custom Fields Storage
- **calendar_events.custom_fields**: JSON array storing field configurations
- **bookings.custom_fields**: Array of field unique keys
- **bookings.custom_fields_value**: Array of user-submitted values

### Example Data Structure
```json
// calendar_events.custom_fields
[
  {
    "unique_key": "company-name-1",
    "type": "text",
    "label": "Company Name",
    "options": [],
    "is_required": true
  },
  {
    "unique_key": "budget-range-2",
    "type": "select",
    "label": "Budget Range",
    "options": ["$1000-$5000", "$5000-$10000", "$10000+"],
    "is_required": false
  }
]

// bookings.custom_fields
["company-name-1", "budget-range-2"]

// bookings.custom_fields_value
["Acme Corp", "$5000-$10000"]
```

## Benefits

1. **Centralized Management**: Custom fields are managed in one place (Settings)
2. **Reusability**: Same fields can be used across multiple events
3. **Flexibility**: Support for various field types and validation
4. **User-Friendly**: Intuitive interface for both admins and end users
5. **Data Integrity**: Proper validation and storage of custom field data

## Migration Notes

- Existing events with hardcoded custom fields will continue to work (backward compatibility)
- New events should use the settings-based custom fields for better management
- Custom field data is preserved during event updates

## Troubleshooting

### No Custom Fields Available
- Ensure custom fields are created in Settings > Custom Fields
- Verify the module is set to "calendar_events"
- Check that fields belong to the correct user/organization

### Fields Not Appearing in Booking Form
- Verify custom fields are added to the event in the Create/Edit Event modal
- Check that the event was saved successfully after adding custom fields
- Ensure the public booking link is for the correct event

### Data Not Saving
- Check browser console for JavaScript errors
- Verify required fields are filled out
- Ensure proper network connectivity during form submission
