# Manual Testing Guide for Custom Fields Integration

## Prerequisites
- Ensure the application is running
- Have admin access to the system
- Database migrations are up to date

## Step-by-Step Testing

### Step 1: Create Custom Fields in Settings

1. **Navigate to Custom Fields Settings**
   - Go to **Settings > Custom Fields** (or `/custom-field`)
   - Click **"Create Custom Field"**

2. **Create Test Custom Fields**
   
   **Field 1: Company Name (Text)**
   - Name: `Company Name`
   - Type: `Text`
   - Module: `Calendar Events`
   - Required: ✓ (checked)
   - Click **Save**

   **Field 2: Budget Range (Select)**
   - Name: `Budget Range`
   - Type: `Dropdown menu`
   - Module: `Calendar Events`
   - Options: Add these options:
     - `$1,000 - $5,000`
     - `$5,000 - $10,000`
     - `$10,000+`
   - Required: ✗ (unchecked)
   - Click **Save**

   **Field 3: Project Type (Radio)**
   - Name: `Project Type`
   - Type: `Radio button group`
   - Module: `Calendar Events`
   - Options: Add these options:
     - `Web Development`
     - `Mobile App`
     - `Consulting`
   - Required: ✓ (checked)
   - Click **Save**

3. **Verify Custom Fields Created**
   - You should see all 3 custom fields listed in the Custom Fields table
   - Each should show "Calendar Events" as the module

### Step 2: Create Event with Custom Fields

1. **Navigate to Smart Scheduler**
   - Go to **Smart Scheduler** (or `/calendar`)
   - Click **"Create/Edit"** tab
   - Click **"Create New Event"** button

2. **Fill Event Details**
   - Title: `Test Event with Custom Fields`
   - Duration: `60` minutes
   - Description: `Testing custom fields integration`

3. **Add Custom Fields**
   - Scroll to **"Custom Fields"** section
   - You should see a dropdown with your created custom fields:
     - Company Name
     - Budget Range
     - Project Type
   - Select **"Company Name"** and click **"Add Field"**
   - Select **"Budget Range"** and click **"Add Field"**
   - Select **"Project Type"** and click **"Add Field"**

4. **Verify Fields Added**
   - You should see 3 disabled input fields:
     - Company Name (text input)
     - Budget Range (dropdown)
     - Project Type (radio buttons)
   - Each field should have a trash icon to remove it

5. **Complete Event Creation**
   - Set up weekly availability (default is fine)
   - Set event status to **"Active"**
   - Click **"Create Event"**

6. **Verify Event Created**
   - Event should appear in the calendar
   - Success message should be displayed

### Step 3: Test Public Booking Form

1. **Access Public Booking Link**
   - Click on the created event in the calendar
   - Copy the public booking link (or navigate to `/calendar-events/{event_id}`)
   - Open the link in a new browser tab/window (or incognito mode)

2. **Verify Custom Fields Display**
   - The booking form should show:
     - Standard fields: Name, Email, Phone
     - **Custom fields section with:**
       - Company Name (text input with red asterisk *)
       - Budget Range (dropdown with options)
       - Project Type (radio buttons with red asterisk *)

3. **Test Form Validation**
   - Try submitting without filling required custom fields
   - Should show validation errors for required fields

4. **Fill and Submit Form**
   - Name: `John Doe`
   - Email: `<EMAIL>`
   - Phone: `+1234567890`
   - Select a date and time slot
   - **Custom Fields:**
     - Company Name: `Acme Corporation`
     - Budget Range: `$5,000 - $10,000`
     - Project Type: `Web Development`
   - Click **"Confirm Booking"**

5. **Verify Booking Success**
   - Should redirect to confirmation page
   - Confirmation should show all details including custom field values

### Step 4: Verify Data in Admin Panel

1. **Check Bookings List**
   - Go back to **Smart Scheduler > Appointments** tab
   - Find the booking you just created
   - Click **"View Details"** or **"Custom Fields"** button

2. **Verify Custom Field Data**
   - Should display:
     - Company Name: Acme Corporation
     - Budget Range: $5,000 - $10,000
     - Project Type: Web Development

### Step 5: Test Different Field Types

Repeat the process with different field types to ensure they work correctly:

- **Date field**: Should show date picker
- **Number field**: Should only accept numbers
- **Email field**: Should validate email format
- **Textarea**: Should show multi-line text area
- **Checkbox**: Should allow multiple selections
- **File upload**: Should allow file selection

## Expected Results

✅ **Success Criteria:**
- Custom fields can be created in settings
- Custom fields appear in event creation form
- Custom fields are properly saved with events
- Public booking form displays custom fields correctly
- Form validation works for required fields
- Booking data includes custom field values
- Admin can view custom field responses

❌ **Failure Indicators:**
- Custom fields dropdown is empty in event creation
- Custom fields don't appear in public booking form
- Form submission fails with custom field data
- Custom field values are not saved or displayed
- JavaScript errors in browser console

## Troubleshooting

### Issue: No custom fields in dropdown
- **Solution**: Ensure custom fields are created with module "Calendar Events"

### Issue: Custom fields not appearing in booking form
- **Solution**: Verify custom fields were added to the event and event was saved

### Issue: Form validation not working
- **Solution**: Check browser console for JavaScript errors

### Issue: Data not saving
- **Solution**: Check network tab for API errors, verify CSRF token

## Browser Testing

Test the functionality in multiple browsers:
- Chrome
- Firefox
- Safari
- Edge

## Mobile Testing

Test the booking form on mobile devices to ensure responsive design works correctly.
