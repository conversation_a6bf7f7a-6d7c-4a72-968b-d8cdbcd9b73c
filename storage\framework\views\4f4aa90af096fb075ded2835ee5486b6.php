
<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('Smart Scheduler')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('action-btn'); ?>
<!-- Navigation Buttons -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body p-3">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="d-flex justify-content-center flex-grow-1 align-items-center">
                        <div class="view-switcher-container">
                            <div class="btn-group view-switcher" role="group">
                                <button type="button" onclick="switchView('calendar')" id="calendar-btn" class="btn bg-primary view-btn">
                                    <i class="ti ti-calendar"></i>
                                    <span class="btn-text"><?php echo e(__('Calendar')); ?></span>
                                </button>
                                <button type="button" onclick="switchView('events')" id="events-btn" class="btn btn-outline-primary view-btn">
                                    <i class="ti ti-calendar-event"></i>
                                    <span class="btn-text"><?php echo e(__('Create/Edit')); ?></span>
                                </button>
                                <!-- <button type="button" onclick="switchView('appointment')" id="appointment-btn" class="btn btn-outline-primary view-btn">
                                    <i class="ti ti-calendar-plus"></i>
                                    <span class="btn-text"><?php echo e(__('Appointment')); ?></span>
                                </button> -->
                                <button type="button" onclick="switchView('bookings')" id="bookings-btn" class="btn btn-outline-primary view-btn">
                                    <i class="ti ti-users"></i>
                                    <span class="btn-text"><?php echo e(__('Appointments')); ?></span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('css-page'); ?>
    <link href='https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.css' rel='stylesheet' />
    <script src='https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js'></script>
    <link href="<?php echo e(asset('css/calendar.css')); ?>" rel="stylesheet" />
    <style>
        /* Date Filter Styling */
        .date-filter-container {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 8px 12px;
            border: 1px solid #e9ecef;
        }

        .date-filter-container .form-control-sm {
            border-radius: 6px;
            border: 1px solid #ced4da;
            font-size: 0.875rem;
        }

        .date-filter-container .btn-sm {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
            border-radius: 6px;
        }

        /* Status Dropdown Styling */
        .status-dropdown .dropdown-toggle {
            border: none;
            padding: 0.25rem 0.75rem;
            font-size: 0.875rem;
            border-radius: 6px;
            min-width: 100px;
        }

        .status-dropdown .dropdown-toggle:focus {
            box-shadow: none;
        }

        .status-dropdown .dropdown-menu {
            border-radius: 8px;
            border: 1px solid #e9ecef;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            min-width: 150px;
        }

        .status-dropdown .dropdown-item {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            border-radius: 4px;
            margin: 2px 4px;
            transition: all 0.2s ease;
        }

        .status-dropdown .dropdown-item:hover {
            background-color: #f8f9fa;
            transform: translateX(2px);
        }

        .status-dropdown .dropdown-item i {
            width: 16px;
            text-align: center;
        }

        .location-count-badge {
            font-size: 0.7rem;
            padding: 0.2rem 0.4rem;
        }

        /* Pagination Styles */
        #events-pagination {
            border-top: 1px solid #dee2e6;
            background-color: #f8f9fa;
        }

        .pagination-info {
            color: #6c757d;
            font-size: 0.875rem;
        }

        .pagination .page-link {
            color: #6c757d;
            border: 1px solid #dee2e6;
            padding: 0.375rem 0.75rem;
            margin: 0 0.125rem;
            border-radius: 0.375rem;
            transition: all 0.15s ease-in-out;
        }

        .pagination .page-link:hover {
            color: #0d6efd;
            background-color: #e9ecef;
            border-color: #dee2e6;
        }

        .pagination .page-item.active .page-link {
            background-color: #0d6efd;
            border-color: #0d6efd;
            color: white;
        }

        .pagination .page-item.disabled .page-link {
            color: #adb5bd;
            background-color: #fff;
            border-color: #dee2e6;
        }

        #events-per-page {
            min-width: 70px;
        }

        /* Bulk Actions Styles */
        #bulk-actions {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 0.5rem 0.75rem;
            background-color: #f8f9fa;
        }

        #selected-count {
            font-size: 0.875rem;
            font-weight: 500;
        }

        #bulk-status {
            min-width: 120px;
        }

        .form-check-input:indeterminate {
            background-color: #0d6efd;
            border-color: #0d6efd;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/%3e%3c/svg%3e");
        }

        .event-checkbox {
            cursor: pointer;
        }

        #select-all-events {
            cursor: pointer;
        }

        /* Appointment Filter Styling */
        .appointment-filter-group .btn {
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .appointment-filter-group .btn:first-child {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }

        .appointment-filter-group .btn:not(:first-child):not(:last-child) {
            border-radius: 0;
            border-left: none;
        }

        .appointment-filter-group .btn:last-child {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
            border-left: none;
        }

        .appointment-filter-group .btn.active {
            background-color: #0d6efd;
            border-color: #0d6efd;
            color: white;
        }

        .appointment-filter-group .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
    </style>
<?php $__env->stopPush(); ?>

<?php
    $setting = \App\Models\Utility::settings();
    // Get custom fields from settings for calendar events
    $customFields = \App\Models\CustomField::where('module', 'calendar_events')
                                          ->where('created_by', Auth::id())
                                          ->get();
?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e(__('Smart Scheduler')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- Create Event Modal -->
<div class="modal fade" id="createEventModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?php echo e(__('Create New Event')); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="createEventForm">
                <div class="modal-body">
                    <?php echo csrf_field(); ?>
                    <!-- PART 1: Event Details -->
                    <div class="form-part">
                        <div class="part-title"><?php echo e(__('Event Details')); ?></div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="event_title" class="form-label"><?php echo e(__(' Title')); ?> *</label>
                                <input type="text" class="form-control" id="event_title" name="title" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="assigned_staff_id" class="form-label"><?php echo e(__('Assign a Staff')); ?></label>
                                <select class="form-control" id="assigned_staff_id" name="assigned_staff_id">
                                    <option value=""><?php echo e(__('Select Staff (Optional)')); ?></option>
                                </select>
                            </div>
                        </div>

                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="event_duration" class="form-label"><?php echo e(__('Duration (Minutes)')); ?></label>
                                <input type="number" class="form-control" id="event_duration" name="duration" min="1" max="1440" value="60">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="booking_per_slot" class="form-label"><?php echo e(__('Booking Per Slot')); ?></label>
                                <input type="number" class="form-control" id="booking_per_slot" name="booking_per_slot" min="1" max="100" value="1">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="minimum_notice_value" class="form-label text-primary fw-semibold"><?php echo e(__('Minimum Scheduling Notice')); ?></label>
                                <div class="d-flex gap-2 align-items-stretch">
                                    <div class="flex-fill">
                                        <input type="number" class="form-control border-primary" id="minimum_notice_value" name="minimum_notice_value" min="0" max="999" value="0" placeholder="0" style="border-width: 2px;">
                                    </div>
                                    <div class="flex-fill">
                                        <select class="form-control border-primary" id="minimum_notice_unit" name="minimum_notice_unit" style="border-width: 2px;">
                                            <option value="minutes"><?php echo e(__('Minutes')); ?></option>
                                            <option value="hours"><?php echo e(__('Hours')); ?></option>
                                            <option value="days"><?php echo e(__('Days')); ?></option>
                                            <option value="months"><?php echo e(__('Months')); ?></option>
                                        </select>
                                    </div>
                                </div>
                                <small class="text-muted mt-1" id="minimum_notice_preview">No minimum notice required</small>
                                <!-- Hidden field to store the calculated minutes for backend compatibility -->
                                <input type="hidden" id="minimum_notice" name="minimum_notice" value="0">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="event_description" class="form-label"><?php echo e(__('Description')); ?></label>
                            <textarea class="form-control" id="event_description" name="description" rows="3"></textarea>
                        </div>

                        <!-- Date Range Section -->
                        <div class="mb-4">
                            <div class="part-title mb-3"><?php echo e(__('Date Range')); ?></div>
                            <p class="text-muted mb-3"><?php echo e(__('Invitees can schedule...')); ?></p>

                            <!-- Calendar Days Option -->
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="radio" name="date_range_type" id="calendar_days_option" value="calendar_days">
                                <label class="form-check-label d-flex align-items-center" for="calendar_days_option">
                                    <div class="d-flex align-items-center gap-2">
                                        <input type="number" class="form-control form-control-sm" id="calendar_days_input" name="date_range_days"
                                               style="width: 80px;" min="1" max="365" value="30" disabled>
                                        <span><?php echo e(__('calendar days into the future')); ?></span>
                                    </div>
                                </label>
                            </div>

                            <!-- Date Range Option -->
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="radio" name="date_range_type" id="date_range_option" value="date_range">
                                <label class="form-check-label d-flex align-items-center" for="date_range_option">
                                    <div class="d-flex align-items-center gap-2">
                                        <span><?php echo e(__('Within a date range')); ?></span>
                                        <input type="date" class="form-control form-control-sm ms-2" id="date_range_start_input" name="date_range_start"
                                               style="width: 150px;" disabled>
                                        <span><?php echo e(__('to')); ?></span>
                                        <input type="date" class="form-control form-control-sm" id="date_range_end_input" name="date_range_end"
                                               style="width: 150px;" disabled>
                                    </div>
                                </label>
                            </div>

                            <!-- Indefinitely Option -->
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="radio" name="date_range_type" id="indefinitely_option" value="indefinitely" checked>
                                <label class="form-check-label" for="indefinitely_option">
                                    <?php echo e(__('Indefinitely into the future')); ?>

                                </label>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-12 mb-3">
                                <label for="event_location" class="form-label"><?php echo e(__('Location')); ?> <span class="text-danger">*</span></label>

                                <!-- Selected Locations Display -->
                                <div id="selected-locations-container" class="mb-2">
                                    <!-- Selected location chips will appear here -->
                                </div>

                                <!-- Location Dropdown -->
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary dropdown-toggle w-100 text-start" type="button" id="locationDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="ti ti-map-pin me-2"></i><?php echo e(__('Add a location')); ?>

                                    </button>
                                    <ul class="dropdown-menu w-100" aria-labelledby="locationDropdown">
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="openLocationModal('zoom')">
                                                <i class="ti ti-video me-2 text-primary"></i>
                                                <div>
                                                    <strong><?php echo e(__('Zoom')); ?></strong>
                                                    <br><small class="text-muted"><?php echo e(__('Online meeting/conference')); ?></small>
                                                </div>
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="openLocationModal('in_person')">
                                                <i class="ti ti-map-pin me-2 text-success"></i>
                                                <div>
                                                    <strong><?php echo e(__('In-person meeting')); ?></strong>
                                                    <br><small class="text-muted"><?php echo e(__('Set an address or place')); ?></small>
                                                </div>
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="openLocationModal('phone')">
                                                <i class="ti ti-phone me-2 text-info"></i>
                                                <div>
                                                    <strong><?php echo e(__('Phone call')); ?></strong>
                                                    <br><small class="text-muted"><?php echo e(__('Incoming or Outgoing calls')); ?></small>
                                                </div>
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="openLocationModal('meet')">
                                                <i class="ti ti-brand-google me-2 text-warning"></i>
                                                <div>
                                                    <strong><?php echo e(__('Google Meet')); ?></strong>
                                                    <br><small class="text-muted"><?php echo e(__('Online meeting/conference')); ?></small>
                                                </div>
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="openLocationModal('skype')">
                                                <i class="ti ti-brand-skype me-2 text-primary"></i>
                                                <div>
                                                    <strong><?php echo e(__('Skype')); ?></strong>
                                                    <br><small class="text-muted"><?php echo e(__('Online meeting/conference')); ?></small>
                                                </div>
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="openLocationModal('others')">
                                                <i class="ti ti-dots me-2 text-secondary"></i>
                                                <div>
                                                    <strong><?php echo e(__('Others')); ?></strong>
                                                    <br><small class="text-muted"><?php echo e(__('Custom location type')); ?></small>
                                                </div>
                                            </a>
                                        </li>
                                    </ul>
                                </div>

                                <!-- Hidden input to store location data -->
                                <input type="hidden" id="event_locations_data" name="locations_data" value="">
                            </div>
                        </div>

                        <!-- Custom Redirect URL Section -->
                        <div class="row">
                            <div class="col-12 mb-3">
                                <label for="custom_redirect_url" class="form-label">
                                    <i class="ti ti-external-link me-2"></i><?php echo e(__('Custom Redirect URL')); ?>

                                    <small class="text-muted">(<?php echo e(__('Optional')); ?>)</small>
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="ti ti-link"></i>
                                    </span>
                                    <input type="url"
                                           class="form-control"
                                           id="custom_redirect_url"
                                           name="custom_redirect_url"
                                           placeholder="<?php echo e(__('https://example.com/thank-you')); ?>"
                                           pattern="https?://.*">
                                </div>
                                <div class="form-text">
                                    <i class="ti ti-info-circle me-1"></i>
                                    <?php echo e(__('If provided, users will be redirected to this URL after booking confirmation instead of the default confirmation page.')); ?>

                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-part">
    <div class="part-title"><?php echo e(__('Custom Fields')); ?></div>
    <div class="row">
        <div class="col-md-6 mb-3">
            <label for="custom_field" class="form-label"><?php echo e(__('Field Type')); ?></label>
            <select class="form-control" id="custom_field" name="custom_field[]" onchange="toggleCustomField()">
                <option value=""><?php echo e(__('No Custom Field')); ?></option>
                <?php if($customFields->count() > 0): ?>
                    <?php $__currentLoopData = $customFields; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $field): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($field->unique_key); ?>" data-field-type="<?php echo e($field->type); ?>" data-field-options="<?php echo e(json_encode($field->options)); ?>">
                            <?php echo e($field->name); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php else: ?>
                    <option disabled><?php echo e(__('No custom fields available. Please create custom fields in Settings > Custom Fields first.')); ?></option>
                <?php endif; ?>
            </select>
        </div>
        <div class="col-md-6 mb-3 d-flex align-items-end">
            <button type="button" class="btn btn-primary" onclick="addCustomField()" <?php if($customFields->count() == 0): ?> disabled <?php endif; ?>>
                <i class="ti ti-plus me-1"></i><?php echo e(__('Add Field')); ?>

            </button>
        </div>
    </div>

    <?php if($customFields->count() == 0): ?>
        <div class="alert alert-info">
            <i class="ti ti-info-circle me-2"></i>
            <?php echo e(__('No custom fields available. Please create custom fields in Settings > Custom Fields with module "calendar_events" to use them in events.')); ?>

            <a href="<?php echo e(route('custom-field.index')); ?>" class="btn btn-sm btn-outline-primary ms-2">
                <i class="ti ti-settings me-1"></i><?php echo e(__('Manage Custom Fields')); ?>

            </a>
        </div>
    <?php endif; ?>

    <!-- Dynamic Custom Fields Container -->
    <div id="custom-fields-container">
        <!-- Multiple custom fields will be added here dynamically -->
    </div>
</div>

<div class="form-part">
    <div class="part-title"><?php echo e(__('Weekly Availability')); ?></div>
    <div class="week-availability-vertical">
        <!-- Monday -->
        <div class="day-availability">
            <div class="day-header">
                <input type="checkbox" id="monday-checkbox" name="availability[monday][enabled]" checked>
                <label for="monday-checkbox"><?php echo e(__('Monday')); ?></label>
                <button type="button" class="btn btn-sm btn-outline-primary ms-2 add-slot-btn" data-day="monday" onclick="addTimeSlot('monday')">
                    <i class="ti ti-plus"></i> Add Slot
                </button>
            </div>
            <div class="day-slots" id="monday-slots">
                <div class="time-slot">
                    <input type="time" name="availability[monday][slots][0][start]" class="form-control" value="09:00" required>
                    <span class="mx-2">to</span>
                    <input type="time" name="availability[monday][slots][0][end]" class="form-control" value="17:00" required>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-slot-btn" onclick="removeTimeSlot('monday', 0)">
                        <i class="ti ti-trash"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Tuesday -->
        <div class="day-availability">
            <div class="day-header">
                <input type="checkbox" id="tuesday-checkbox" name="availability[tuesday][enabled]" checked>
                <label for="tuesday-checkbox"><?php echo e(__('Tuesday')); ?></label>
                <button type="button" class="btn btn-sm btn-outline-primary ms-2 add-slot-btn" data-day="tuesday" onclick="addTimeSlot('tuesday')">
                    <i class="ti ti-plus"></i> Add Slot
                </button>
            </div>
            <div class="day-slots" id="tuesday-slots">
                <div class="time-slot">
                    <input type="time" name="availability[tuesday][slots][0][start]" class="form-control" value="09:00" required>
                    <span class="mx-2">to</span>
                    <input type="time" name="availability[tuesday][slots][0][end]" class="form-control" value="17:00" required>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-slot-btn" onclick="removeTimeSlot('tuesday', 0)">
                        <i class="ti ti-trash"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Wednesday -->
        <div class="day-availability">
            <div class="day-header">
                <input type="checkbox" id="wednesday-checkbox" name="availability[wednesday][enabled]" checked>
                <label for="wednesday-checkbox"><?php echo e(__('Wednesday')); ?></label>
                <button type="button" class="btn btn-sm btn-outline-primary ms-2 add-slot-btn" data-day="wednesday" onclick="addTimeSlot('wednesday')">
                    <i class="ti ti-plus"></i> Add Slot
                </button>
            </div>
            <div class="day-slots" id="wednesday-slots">
                <div class="time-slot">
                    <input type="time" name="availability[wednesday][slots][0][start]" class="form-control" value="09:00" required>
                    <span class="mx-2">to</span>
                    <input type="time" name="availability[wednesday][slots][0][end]" class="form-control" value="17:00" required>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-slot-btn" onclick="removeTimeSlot('wednesday', 0)">
                        <i class="ti ti-trash"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Thursday -->
        <div class="day-availability">
            <div class="day-header">
                <input type="checkbox" id="thursday-checkbox" name="availability[thursday][enabled]" checked>
                <label for="thursday-checkbox"><?php echo e(__('Thursday')); ?></label>
                <button type="button" class="btn btn-sm btn-outline-primary ms-2 add-slot-btn" data-day="thursday" onclick="addTimeSlot('thursday')">
                    <i class="ti ti-plus"></i> Add Slot
                </button>
            </div>
            <div class="day-slots" id="thursday-slots">
                <div class="time-slot">
                    <input type="time" name="availability[thursday][slots][0][start]" class="form-control" value="09:00" required>
                    <span class="mx-2">to</span>
                    <input type="time" name="availability[thursday][slots][0][end]" class="form-control" value="17:00" required>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-slot-btn" onclick="removeTimeSlot('thursday', 0)">
                        <i class="ti ti-trash"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Friday -->
        <div class="day-availability">
            <div class="day-header">
                <input type="checkbox" id="friday-checkbox" name="availability[friday][enabled]" checked>
                <label for="friday-checkbox"><?php echo e(__('Friday')); ?></label>
                <button type="button" class="btn btn-sm btn-outline-primary ms-2 add-slot-btn" data-day="friday" onclick="addTimeSlot('friday')">
                    <i class="ti ti-plus"></i> Add Slot
                </button>
            </div>
            <div class="day-slots" id="friday-slots">
                <div class="time-slot">
                    <input type="time" name="availability[friday][slots][0][start]" class="form-control" value="09:00" required>
                    <span class="mx-2">to</span>
                    <input type="time" name="availability[friday][slots][0][end]" class="form-control" value="17:00" required>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-slot-btn" onclick="removeTimeSlot('friday', 0)">
                        <i class="ti ti-trash"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Saturday -->
        <div class="day-availability">
            <div class="day-header">
                <input type="checkbox" id="saturday-checkbox" name="availability[saturday][enabled]" checked>
                <label for="saturday-checkbox"><?php echo e(__('Saturday')); ?></label>
                <button type="button" class="btn btn-sm btn-outline-primary ms-2 add-slot-btn" data-day="saturday" onclick="addTimeSlot('saturday')">
                    <i class="ti ti-plus"></i> Add Slot
                </button>
            </div>
            <div class="day-slots" id="saturday-slots">
                <div class="time-slot">
                    <input type="time" name="availability[saturday][slots][0][start]" class="form-control" value="09:00" required>
                    <span class="mx-2">to</span>
                    <input type="time" name="availability[saturday][slots][0][end]" class="form-control" value="17:00" required>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-slot-btn" onclick="removeTimeSlot('saturday', 0)">
                        <i class="ti ti-trash"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Sunday -->
        <div class="day-availability">
            <div class="day-header">
                <input type="checkbox" id="sunday-checkbox" name="availability[sunday][enabled]" checked>
                <label for="sunday-checkbox"><?php echo e(__('Sunday')); ?></label>
                <button type="button" class="btn btn-sm btn-outline-primary ms-2 add-slot-btn" data-day="sunday" onclick="addTimeSlot('sunday')">
                    <i class="ti ti-plus"></i> Add Slot
                </button>
            </div>
            <div class="day-slots" id="sunday-slots">
                <div class="time-slot">
                    <input type="time" name="availability[sunday][slots][0][start]" class="form-control" value="09:00" required>
                    <span class="mx-2">to</span>
                    <input type="time" name="availability[sunday][slots][0][end]" class="form-control" value="17:00" required>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-slot-btn" onclick="removeTimeSlot('sunday', 0)">
                        <i class="ti ti-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="mb-4">
    <div class="row">
        <div class="col-md-8">
            <label for="override_date" class="form-label" style="margin-left: 10px;"><?php echo e(__('Add date overrides')); ?></label>
            <div class="row g-2 mb-3">
                <div class="col-md-4">
                    <input type="date" class="form-control" id="override_date" placeholder="Select date" style="border-radius: 8px;">
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="override_time" style="border-radius: 8px;">
                        <option value="">Select time</option>
                        <option value="00:00">12:00 AM</option>
                        <option value="00:30">12:30 AM</option>
                        <option value="01:00">01:00 AM</option>
                        <option value="01:30">01:30 AM</option>
                        <option value="02:00">02:00 AM</option>
                        <option value="02:30">02:30 AM</option>
                        <option value="03:00">03:00 AM</option>
                        <option value="03:30">03:30 AM</option>
                        <option value="04:00">04:00 AM</option>
                        <option value="04:30">04:30 AM</option>
                        <option value="05:00">05:00 AM</option>
                        <option value="05:30">05:30 AM</option>
                        <option value="06:00">06:00 AM</option>
                        <option value="06:30">06:30 AM</option>
                        <option value="07:00">07:00 AM</option>
                        <option value="07:30">07:30 AM</option>
                        <option value="08:00">08:00 AM</option>
                        <option value="08:30">08:30 AM</option>
                        <option value="09:00">09:00 AM</option>
                        <option value="09:30">09:30 AM</option>
                        <option value="10:00">10:00 AM</option>
                        <option value="10:30">10:30 AM</option>
                        <option value="11:00">11:00 AM</option>
                        <option value="11:30">11:30 AM</option>
                        <option value="12:00">12:00 PM</option>
                        <option value="12:30">12:30 PM</option>
                        <option value="13:00">01:00 PM</option>
                        <option value="13:30">01:30 PM</option>
                        <option value="14:00">02:00 PM</option>
                        <option value="14:30">02:30 PM</option>
                        <option value="15:00">03:00 PM</option>
                        <option value="15:30">03:30 PM</option>
                        <option value="16:00">04:00 PM</option>
                        <option value="16:30">04:30 PM</option>
                        <option value="17:00">05:00 PM</option>
                        <option value="17:30">05:30 PM</option>
                        <option value="18:00">06:00 PM</option>
                        <option value="18:30">06:30 PM</option>
                        <option value="19:00">07:00 PM</option>
                        <option value="19:30">07:30 PM</option>
                        <option value="20:00">08:00 PM</option>
                        <option value="20:30">08:30 PM</option>
                        <option value="21:00">09:00 PM</option>
                        <option value="21:30">09:30 PM</option>
                        <option value="22:00">10:00 PM</option>
                        <option value="22:30">10:30 PM</option>
                        <option value="23:00">11:00 PM</option>
                        <option value="23:30">11:30 PM</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <button type="button" class="btn btn-primary w-100" onclick="addUnavailableSlot()" style="border-radius: 8px;">
                        <i class="ti ti-plus me-1"></i><?php echo e(__('Add')); ?>

                    </button>
                </div>
            </div>
            <div id="unavailable-slots-list" class="mt-3">
                <!-- Dynamically added slots go here -->
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="form-part">
                <label for="event_status" class="form-label"><?php echo e(__('Event Status')); ?> *</label>
                <select class="form-control" id="event_status" name="status" required>
                    <option value="active"><?php echo e(__('Active')); ?></option>
                    <option value="inactive"><?php echo e(__('Inactive')); ?></option>
                </select>
                <small class="text-muted"><?php echo e(__('Active events can be booked by users. Inactive events are hidden from booking.')); ?></small>
            </div>
        </div>
    </div>
</div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('Cancel')); ?></button>
                    <button type="submit" class="btn btn-primary" id="submitBtn"><?php echo e(__('Create Event')); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>
</div>
<!-- View Event Modal -->
<div class="modal fade" id="viewEventModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="ti ti-calendar-event me-2"></i><?php echo e(__('Event Details')); ?>

                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-4">
                <!-- Event Title -->
                <div class="text-center mb-4 pb-3 border-bottom">
                    <h3 class="text-primary mb-1" id="view-event-title"></h3>
                    <small class="text-muted">Event Information</small>
                </div>
                
                <!-- Event Details Grid -->
                <div class="row g-4">
                    <!-- Event Settings Section -->
                    <div class="col-12">
                        <h6 class="text-secondary mb-3">
                            <i class="ti ti-settings me-2"></i>Event Settings
                        </h6>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="text-center p-3 border rounded">
                                    <i class="ti ti-clock text-info fs-3 mb-2"></i>
                                    <div class="fw-bold text-dark">Duration</div>
                                    <div class="text-muted" id="view-event-duration"></div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center p-3 border rounded">
                                    <i class="ti ti-users text-warning fs-3 mb-2"></i>
                                    <div class="fw-bold text-dark">Booking Slots</div>
                                    <div class="text-muted" id="view-event-booking-slots"></div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center p-3 border rounded">
                                    <i class="ti ti-bell text-secondary fs-3 mb-2"></i>
                                    <div class="fw-bold text-dark">Notice Required</div>
                                    <div class="text-muted" id="view-event-notice"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Description Section -->
                    <div class="col-12" id="view-description-row" style="display: none;">
                        <h6 class="text-secondary mb-3">
                            <i class="ti ti-file-text me-2"></i>Description
                        </h6>
                        <div class="p-3 bg-light rounded">
                            <div id="view-event-description" class="text-dark"></div>
                        </div>
                    </div>
                    
                    <!-- Location Section -->
                    <div class="col-12" id="view-location-section" style="display: none;">
                        <div class="d-flex align-items-center mb-4">
                            <div class="icon-wrapper-modern me-3">
                                <i class="ti ti-map-pin text-primary"></i>
                            </div>
                            <div>
                                <h5 class="fw-bold text-dark mb-0">Location Details</h5>
                                <small class="text-muted">Where this event will take place</small>
                            </div>
                        </div>

                        <!-- Locations Container with Dynamic Column Layout -->
                        <div class="row g-3" id="view-locations-container">
                            <!-- Physical Address -->
                            <div id="view-address-row" style="display: none;">
                                <div class="p-4 border rounded bg-white shadow-sm h-100">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="flex-shrink-0">
                                            <div class="icon-wrapper-modern">
                                                <i class="ti ti-map-pin text-success fs-5"></i>
                                            </div>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <h6 class="fw-bold text-dark mb-0">Physical Address</h6>
                                            <small class="text-muted">Meeting location</small>
                                        </div>
                                    </div>
                                    <div class="address-content">
                                        <p class="mb-0 text-dark" id="view-event-address"></p>
                                    </div>
                                </div>
                            </div>

                            <!-- Meeting Locations -->
                            <div id="view-link-row" style="display: none;">
                                <div class="p-4 border rounded bg-white shadow-sm h-100">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="flex-shrink-0">
                                            <div class="icon-wrapper-modern">
                                                <i class="ti ti-video text-primary fs-5"></i>
                                            </div>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <h6 class="fw-bold text-dark mb-0">Meeting Locations</h6>
                                            <small class="text-muted">Click to join</small>
                                        </div>
                                    </div>
                                    <div id="view-meeting-locations-container" class="modern-locations-list">
                                        <!-- Multiple meeting locations will be displayed here -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Legacy Location Type Display (for backward compatibility) -->
                        <div class="mb-3" id="view-location-row" style="display: none;">
                            <div class="d-flex align-items-center p-3 border rounded">
                                <div class="flex-shrink-0">
                                    <i class="ti ti-map-pin text-success fs-4"></i>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <div class="fw-bold text-dark">Location Type</div>
                                    <div class="text-muted" id="view-event-location"></div>
                                </div>
                            </div>
                        </div>
                </div>
                </div>
            </div>
            <div class="modal-footer bg-light">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="ti ti-x me-1"></i><?php echo e(__('Close')); ?>

                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modern Calendar Section -->
<div class="row" id="calendar-section">
    <div class="col-xl-9 col-lg-8 col-md-12 mb-4">
        <div class="modern-calendar-card">
            <div class="modern-calendar-header">
                <div class="calendar-title-section">
                    <div class="calendar-icon-wrapper bg-primary">
                        <i class="fas fa-address-card"></i>
                    </div>
                    <div>
                        <!-- <h4 class="calendar-title"><?php echo e(__('Calendar')); ?></h4> -->
                        <h5><?php echo e(__('Manage your Appointments')); ?></h5>
                    </div>
                </div>
                <!-- Modern Legend -->
                <div class="modern-legend">
                    <div class="legend-item-modern">
                        <div class="legend-dot events-dot"></div>
                        <span><?php echo e(__('Events')); ?></span>
                    </div>
                    <div class="legend-item-modern">
                        <div class="legend-dot bookings-dot"></div>
                        <span><?php echo e(__('Bookings')); ?></span>
                    </div>
                </div>
            </div>
            <div class="modern-calendar-body">
                <div id='calendar' class="modern-fullcalendar"></div>
            </div>
        </div>
    </div>

    <!-- Modern Sidebar -->
    <div class="col-xl-3 col-lg-4 col-md-12 mb-4">
        <div class="modern-sidebar-card">
            <div class="sidebar-header">
                <div class="date-display">
                    <div class="current-date">
                        <h5 class="date-title" id="selected-date-title"><?php echo e(__('Today')); ?></h5>
                        <p class="date-subtitle" id="selected-date-subtitle"><?php echo e(date('F j, Y')); ?></p>
                    </div>
                    <div class="date-icon">
                        <div class="icon-circle">
                            <i class="ti ti-calendar-time"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="sidebar-body">
                <div id="selected-date-events">
                    <div class="empty-state" id="no-events-message">
                        <div class="empty-icon">
                            <i class="ti ti-calendar-off"></i>
                        </div>
                        <h6 class="empty-title"><?php echo e(__('No Events')); ?></h6>
                        <p class="empty-text"><?php echo e(__('Click on a date to view events')); ?></p>
                    </div>
                    <div class="events-container" id="events-list" style="display: none;">
                        <!-- Events will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Events Section -->
<div class="row" id="events-section" style="display: none;">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><?php echo e(__('Events List')); ?></h5>
                <div class="d-flex align-items-center gap-3">
                    <!-- Bulk Actions (hidden by default) -->
                    <div id="bulk-actions" class="d-flex align-items-center gap-2" style="display: none;">
                        <span class="text-muted" id="selected-count">0 selected</span>
                        <select id="bulk-status" class="form-select form-select-sm" style="width: auto;">
                            <option value=""><?php echo e(__('Change Status')); ?></option>
                            <option value="active"><?php echo e(__('Active')); ?></option>
                            <option value="inactive"><?php echo e(__('Inactive')); ?></option>
                        </select>
                        <button id="bulk-delete-btn" class="btn btn-danger btn-sm" title="<?php echo e(__('Delete Selected')); ?>">
                            <i class="ti ti-trash"></i>
                        </button>
                    </div>

                    <!-- View Limit Selector -->
                    <div class="d-flex align-items-center">
                        <label for="events-per-page" class="form-label mb-0 me-2"><?php echo e(__('Show')); ?>:</label>
                        <select id="events-per-page" class="form-select form-select-sm" style="width: auto;">
                            <option value="5">5</option>
                            <option value="10" selected>10</option>
                            <option value="25">25</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                    </div>
                    <div class="action-buttons">
                        <button class="id="create-event-btn" onclick="openCreateEventModal()" style="background: linear-gradient(to right, #0f5132, #0d6efd); color: white; border: none; padding: 12px 16px; border-radius: 8px; display: inline-flex; align-items: center; font-weight: 600;">
                            <i class="fa fa-plus-circle"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped" id="events-table">
                        <thead>
                            <tr>
                                <th width="40">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="select-all-events">
                                        <label class="form-check-label" for="select-all-events"></label>
                                    </div>
                                </th>
                                <th><?php echo e(__('Title')); ?></th>
                                <th><?php echo e(__('Assigned Staff')); ?></th>
                                <th><?php echo e(__('Date Range')); ?></th>
                                <th><?php echo e(__('Duration')); ?></th>
                                <th><?php echo e(__('Location')); ?></th>
                                <th><?php echo e(__('Status')); ?></th>
                                <th><?php echo e(__('Appointments')); ?></th>
                                <th><?php echo e(__('Actions')); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="9" class="text-center"><?php echo e(__('Loading events...')); ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination Controls -->
                <div class="card-footer d-flex justify-content-between align-items-center" id="events-pagination" style="display: none;">
                    <div class="pagination-info">
                        <span id="pagination-info-text"><?php echo e(__('Showing 0 to 0 of 0 entries')); ?></span>
                    </div>
                    <nav aria-label="Events pagination">
                        <ul class="pagination pagination-sm mb-0" id="pagination-controls">
                            <!-- Pagination buttons will be generated here -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Appointment Section -->
<!-- Bookings Section -->
<div class="row" id="bookings-section" style="display: none;">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><?php echo e(__('All Appointments')); ?></h5>
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create booking')): ?>
                    <div class="d-flex gap-2 align-items-center">
                        <!-- Bulk Actions (hidden by default) -->
                        <div id="bulk-booking-actions" class="d-flex align-items-center gap-2" style="display: none;">
                            <span class="text-muted" id="selected-booking-count">0</span>
                            <button id="bulk-cancel-btn" class="btn btn-sm btn-danger" title="<?php echo e(__('Cancel Selected Appointments')); ?>">
                                <i class="ti ti-x me-1"></i><?php echo e(__('Bulk Cancel')); ?>

                            </button>
                            <div class="vr"></div>
                        </div>
                        
                        <!-- Date Range Filter -->
                        <div class="d-flex gap-2 align-items-center date-filter-container">
                            <label class="form-label mb-0 text-nowrap"><?php echo e(__('Date Range:')); ?></label>
                            <input type="date" id="filter-start-date" class="form-control form-control-sm" style="width: 130px;">
                            <span class="text-muted"><?php echo e(__('to')); ?></span>
                            <input type="date" id="filter-end-date" class="form-control form-control-sm" style="width: 130px;">
                            <button type="button" class="btn btn-sm btn-primary" onclick="applyDateFilter()">
                                <i class="ti ti-filter"></i><?php echo e(__('')); ?>

                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearDateFilter()">
                                <?php echo e(__('Clear')); ?>

                            </button>
                        </div>
                        <div class="vr"></div>
                        
                        <!-- All Appointments Filter -->
                        <div class="d-flex gap-2 align-items-center">
                            <div class="btn-group appointment-filter-group" role="group">
                                <button type="button" class="btn btn-primary btn-sm" id="upcoming-filter-btn" onclick="filterAppointments('upcoming')">
                                    <i class="ti ti-calendar-plus me-1"></i><?php echo e(__('Upcoming')); ?>

                                </button>
                                <button type="button" class="btn btn-outline-primary btn-sm" id="past-filter-btn" onclick="filterAppointments('past')">
                                    <i class="ti ti-calendar-minus me-1"></i><?php echo e(__('Past')); ?>

                                </button>
                                <button type="button" class="btn btn-outline-primary btn-sm" id="cancelled-filter-btn" onclick="filterAppointments('cancelled')">
                                    <i class="ti ti-calendar-x me-1"></i><?php echo e(__('Cancelled')); ?>

                                </button>
                            </div>
                        </div>
                        
                        <div class="vr"></div>
                        <button type="button" class="btn btn-outline-secondary" onclick="refreshBookings()">
                            <i class="ti ti-refresh"></i><?php echo e(__('')); ?>

                        </button>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            <div class="card-body">
                <!-- Alert Messages -->
                <div id="bookings-alert-container" style="display: none;">
                    <div class="alert alert-dismissible fade show" role="alert" id="bookings-alert-message">
                        <span id="bookings-alert-text"></span>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </div>

                <div class="table-responsive" style="overflow-x: auto; white-space: nowrap;">
                    <table class="table table-striped" id="bookings-table" style="min-width: 900px;">
                        <thead>
                            <tr>
                                <th width="40">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="select-all-bookings">
                                        <label class="form-check-label" for="select-all-bookings"></label>
                                    </div>
                                </th>
                                <th style="min-width: 60px;"><?php echo e(__('ID')); ?></th>
                                <th style="min-width: 150px;"><?php echo e(__('Event Title')); ?></th>
                                <th style="min-width: 120px;"><?php echo e(__('Assigned Staff')); ?></th>
                                <th style="min-width: 120px;"><?php echo e(__('Name')); ?></th>
                                <th style="min-width: 180px;"><?php echo e(__('Email')); ?></th>
                                <th style="min-width: 120px;"><?php echo e(__('Phone')); ?></th>
                                <th style="min-width: 100px;"><?php echo e(__('Date')); ?></th>
                                <th style="min-width: 80px;"><?php echo e(__('Time')); ?></th>
                                <th style="min-width: 120px;"><?php echo e(__('Status')); ?></th>
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage booking')): ?>
                                <th style="min-width: 100px;"><?php echo e(__('Actions')); ?></th>
                                <?php endif; ?>
                            </tr>
                        </thead>
                        <tbody id="bookings-tbody">
                            <tr>
                                <td colspan="<?php echo e(Auth::user() && Auth::user()->can('manage booking') ? '11' : '10'); ?>" class="text-center py-4">
                                    <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                    <?php echo e(__('Loading bookings...')); ?>

                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Appointment Booking Modal -->
<div class="modal fade" id="appointmentBookingModal" tabindex="-1" aria-labelledby="appointmentBookingModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="appointmentBookingModalLabel">
                    <i class="ti ti-calendar-plus me-2"></i><?php echo e(__('Book Appointment')); ?>

                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="appointmentBookingForm">
                <?php echo csrf_field(); ?>
                <div class="modal-body">
                    <div class="row">
                        <!-- Contact Name -->
                        <div class="col-md-6 mb-3">
                            <label for="appointment_contact_name" class="form-label"><?php echo e(__('Contact Name')); ?> *</label>
                            <input type="text" class="form-control" id="appointment_contact_name" name="contact_name" required>
                        </div>

                        <!-- Calendar Event -->
                        <div class="col-md-6 mb-3">
                            <label for="appointment_calendar_event" class="form-label"><?php echo e(__('Calendar Event')); ?> *</label>
                            <select class="form-control" id="appointment_calendar_event" name="calendar_event_id" required onchange="updateEventDetails()">
                                <option value=""><?php echo e(__('Select an event')); ?></option>
                                <!-- Events will be populated dynamically -->
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Event Location Type -->
                        <div class="col-md-6 mb-3">
                            <label for="appointment_location_type" class="form-label"><?php echo e(__('Event Location')); ?></label>
                            <select class="form-control" id="appointment_location_type" name="location_type" onchange="updateLocationValue()">
                                <option value=""><?php echo e(__('Select a location')); ?></option>
                                <!-- Locations will be populated dynamically -->
                            </select>
                        </div>

                        <!-- Event Location Value -->
                        <div class="col-md-6 mb-3">
                            <label for="appointment_location_value" class="form-label"><?php echo e(__('Event Location Value')); ?></label>
                            <input type="text" class="form-control" id="appointment_location_value" name="location_value" readonly>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Event Date -->
                        <div class="col-md-6 mb-3">
                            <label for="appointment_date" class="form-label"><?php echo e(__('Event Date')); ?> *</label>
                            <input type="date" class="form-control" id="appointment_date" name="event_date" required readonly>
                        </div>

                        <!-- Timezone Selection -->
                        <div class="col-md-6 mb-3">
                            <label for="appointment_timezone" class="form-label"><?php echo e(__('Timezone')); ?> *</label>
                            <select class="form-control" id="appointment_timezone" name="timezone" required>
                                <option value=""><?php echo e(__('Select timezone')); ?></option>
                                <!-- Timezones will be populated by JavaScript -->
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Time Slot -->
                        <div class="col-md-12 mb-3">
                            <label for="appointment_timeslot" class="form-label"><?php echo e(__('Time Slot')); ?> *</label>
                            <select class="form-control" id="appointment_timeslot" name="timeslot" required>
                                <option value=""><?php echo e(__('Select a time slot')); ?></option>
                                <!-- Time slots will be populated dynamically -->
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="ti ti-x me-1"></i><?php echo e(__('Cancel')); ?>

                    </button>
                    <button type="submit" class="btn btn-success" id="appointmentSubmitBtn">
                        <i class="ti ti-check me-1"></i><?php echo e(__('Book Appointment')); ?>

                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Toast Notification for Copy Event Link -->
<div id="copy-event-toast" class="copy-event-toast" role="alert" aria-live="polite" aria-atomic="true" style="display:none;">
    <div class="toast-body d-flex align-items-center">
        <i class="ti ti-check-circle me-2 text-success fs-5"></i>
        <span id="copy-event-toast-message">Event link copied!</span>
        <button type="button" class="btn-close ms-auto" aria-label="Close" onclick="hideCopyEventToast()"></button>
    </div>
</div>

<?php $__env->stopSection(); ?>
<?php $__env->startPush('script-page'); ?>
<script>

//dayslots
// Function to toggle day slots visibility
function toggleDaySlots(day) {
    const slotsDiv = document.getElementById(day + '-slots');
    const dayCard = document.querySelector(`[data-day="${day}"]`);
    
    if (slotsDiv.style.display === 'none') {
        slotsDiv.style.display = 'block';
        dayCard.classList.add('active');
    } else {
        slotsDiv.style.display = 'none';
        dayCard.classList.remove('active');
    }
}

// Function to add time slot
// Function to add time slot
function addTimeSlot(day) {
    const slotsContainer = document.getElementById(day + '-slots');
    const slotCount = slotsContainer.children.length;
    
    const slotHtml = `
        <div class="time-slot">
            <input type="time" name="availability[${day}][slots][${slotCount}][start]" class="form-control" required>
            <span class="mx-2">to</span>
            <input type="time" name="availability[${day}][slots][${slotCount}][end]" class="form-control" required>
            <button type="button" class="btn btn-sm btn-outline-danger remove-slot-btn" onclick="removeTimeSlot('${day}', ${slotCount})">
                <i class="ti ti-trash"></i>
            </button>
        </div>
    `;
    
    slotsContainer.insertAdjacentHTML('beforeend', slotHtml);
}

// Function to remove time slot
function removeTimeSlot(day, slotIndex) {
    const slotsContainer = document.getElementById(day + '-slots');
    const slotToRemove = slotsContainer.children[slotIndex];
    if (slotToRemove) {
        slotToRemove.remove();
    }
}

// Make functions global
window.toggleDaySlots = toggleDaySlots;
window.addTimeSlot = addTimeSlot;
window.removeTimeSlot = removeTimeSlot;
//day slots ends

// Date Range Functions
function handleDateRangeChange() {
    const calendarDaysOption = document.getElementById('calendar_days_option');
    const dateRangeOption = document.getElementById('date_range_option');
    const indefinitelyOption = document.getElementById('indefinitely_option');

    const calendarDaysInput = document.getElementById('calendar_days_input');
    const dateRangeStartInput = document.getElementById('date_range_start_input');
    const dateRangeEndInput = document.getElementById('date_range_end_input');

    // Reset all inputs
    calendarDaysInput.disabled = true;
    dateRangeStartInput.disabled = true;
    dateRangeEndInput.disabled = true;

    // Enable inputs based on selection
    if (calendarDaysOption.checked) {
        calendarDaysInput.disabled = false;
        calendarDaysInput.required = true;
        dateRangeStartInput.required = false;
        dateRangeEndInput.required = false;
    } else if (dateRangeOption.checked) {
        dateRangeStartInput.disabled = false;
        dateRangeEndInput.disabled = false;
        dateRangeStartInput.required = true;
        dateRangeEndInput.required = true;
        calendarDaysInput.required = false;
    } else if (indefinitelyOption.checked) {
        calendarDaysInput.required = false;
        dateRangeStartInput.required = false;
        dateRangeEndInput.required = false;
    }
}

// Initialize date range event listeners
document.addEventListener('DOMContentLoaded', function() {
    const dateRangeRadios = document.querySelectorAll('input[name="date_range_type"]');
    dateRangeRadios.forEach(radio => {
        radio.addEventListener('change', handleDateRangeChange);
    });

    // Set initial state
    handleDateRangeChange();
});

// Make date range function global
window.handleDateRangeChange = handleDateRangeChange;


//appoointment starts


// Function to switch appointment views
function switchAppointmentView(viewType) {
    console.log('Switching to appointment view:', viewType);
    
    // Update buttons
    $('#upcoming-btn, #past-btn, #daterange-btn, #canceled-btn').removeClass('btn-primary active').addClass('btn-outline-primary');
    $('#' + viewType + '-btn').removeClass('btn-outline-primary').addClass('btn-primary active');
    
    // Hide all appointment sections
    $('#upcoming-appointments, #past-appointments, #daterange-appointments, #canceled-appointments').hide();
    
    // Show/hide date range filter
    if (viewType === 'daterange') {
        $('#date-range-filter').show();
        $('#daterange-appointments').show();
    } else {
        $('#date-range-filter').hide();
        $('#' + viewType + '-appointments').show();
    }
    
    // Load data based on view type
    loadAppointmentData(viewType);
}

// Function to load appointment data
function loadAppointmentData(viewType) {
    console.log('Loading appointment data for:', viewType);
    
    if (viewType === 'upcoming') {
        loadUpcomingAppointments();
    } else if (viewType === 'past') {
        loadPastAppointments();
    } else if (viewType === 'canceled') {
        loadCanceledAppointments();
    }
}

// Function to load upcoming appointments
function loadUpcomingAppointments() {
    $.ajax({
        url: '<?php echo e(route("calendar-events.index")); ?>',
        method: 'GET',
        success: function(response) {
            const tbody = $('#upcoming-appointments-tbody');
            tbody.empty();
            
            if (response.success && response.data && response.data.length > 0) {
                const now = new Date();
                const upcomingEvents = response.data.filter(event => {
                    // Add null check for event and required properties
                    if (!event || !event.start_date || !event.id) {
                        console.warn('Invalid upcoming event data:', event);
                        return false;
                    }
                    const eventDate = new Date(event.start_date);
                    return eventDate > now; // Only future events
                });

                if (upcomingEvents.length > 0) {
                    upcomingEvents.forEach(function(event) {
                        // Additional safety check
                        if (!event || !event.id) {
                            console.warn('Skipping invalid upcoming event:', event);
                            return;
                        }
                        const startDate = new Date(event.start_date);
                        const formattedDate = startDate.toLocaleDateString() + ' ' + startDate.toLocaleTimeString('en-US', {
                            hour: '2-digit',
                            minute: '2-digit'
                        });
                        
                        const row = `
                            <tr>
                                <td>${event.title}</td>
                                <td>${formattedDate}</td>
                                <td>${event.duration || 60} min</td>
                                <td><span class="badge bg-success">Upcoming</span></td>
                                <td>
                                    <button class="btn btn-sm btn-info me-1" onclick="viewEvent(${event.id})" title="View Details">
                                        <i class="ti ti-eye"></i>
                                    </button>
                                    <button class="btn btn-sm btn-warning me-1" onclick="editEvent(${event.id})" title="Edit">
                                        <i class="ti ti-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger" onclick="deleteEvent(${event.id})" title="Cancel">
                                        <i class="ti ti-x"></i>
                                    </button>
                                </td>
                            </tr>
                        `;
                        tbody.append(row);
                    });
                } else {
                    tbody.html('<tr><td colspan="5" class="text-center text-muted">No upcoming appointments found</td></tr>');
                }
            } else {
                tbody.html('<tr><td colspan="5" class="text-center text-muted">No upcoming appointments found</td></tr>');
            }
        },
        error: function(xhr) {
            console.log('Error loading upcoming appointments:', xhr.responseText);
            $('#upcoming-appointments-tbody').html('<tr><td colspan="5" class="text-center text-danger">Error loading appointments</td></tr>');
        }
    });
}

// Function to load past appointments
function loadPastAppointments() {
    $.ajax({
        url: '<?php echo e(route("calendar-events.index")); ?>',
        method: 'GET',
        success: function(response) {
            const tbody = $('#past-appointments-tbody');
            tbody.empty();
            
            if (response.success && response.data && response.data.length > 0) {
                const now = new Date();
                const pastEvents = response.data.filter(event => {
                    // Add null check for event and required properties
                    if (!event || !event.end_date || !event.id) {
                        console.warn('Invalid past event data:', event);
                        return false;
                    }
                    const eventDate = new Date(event.end_date);
                    return eventDate < now; // Only past events
                });

                if (pastEvents.length > 0) {
                    pastEvents.forEach(function(event) {
                        // Additional safety check
                        if (!event || !event.id) {
                            console.warn('Skipping invalid past event:', event);
                            return;
                        }
                        const startDate = new Date(event.start_date);
                        const formattedDate = startDate.toLocaleDateString() + ' ' + startDate.toLocaleTimeString('en-US', {
                            hour: '2-digit',
                            minute: '2-digit'
                        });
                        
                        const row = `
                            <tr>
                                <td>${event.title}</td>
                                <td>${formattedDate}</td>
                                <td>${event.duration || 60} min</td>
                                <td><span class="badge bg-secondary">Completed</span></td>
                                <td>
                                    <button class="btn btn-sm btn-info" onclick="viewEvent(${event.id})" title="View Details">
                                        <i class="ti ti-eye"></i>
                                    </button>
                                </td>
                            </tr>
                        `;
                        tbody.append(row);
                    });
                } else {
                    tbody.html('<tr><td colspan="5" class="text-center text-muted">No past appointments found</td></tr>');
                }
            } else {
                tbody.html('<tr><td colspan="5" class="text-center text-muted">No past appointments found</td></tr>');
            }
        },
        error: function(xhr) {
            console.log('Error loading past appointments:', xhr.responseText);
            $('#past-appointments-tbody').html('<tr><td colspan="5" class="text-center text-danger">Error loading appointments</td></tr>');
        }
    });
}

function loadCanceledAppointments() {
    console.log('Loading canceled appointments...');
    const tbody = $('#bookings-tbody');
    tbody.html(`
        <tr>
            <td colspan="<?php echo e(Auth::user() && Auth::user()->can('manage booking') ? '11' : '10'); ?>" class="text-center py-4">
                <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                <?php echo e(__('Loading canceled appointments...')); ?>

            </td>
        </tr>
    `);

    $.ajax({
        url: '<?php echo e(route("bookings.index")); ?>',
        method: 'GET',
        data: {
            status: 'canceled'
        },
        success: function(response) {
            tbody.empty();

            try {
                let $response = $(response);
                let $bookingRows = $response.find('#bookings-table tbody tr');

                if ($bookingRows.length > 0) {
                    // Check if it's the "no bookings" row
                    let firstRowText = $bookingRows.first().text().trim();
                    if (firstRowText.includes('No bookings found') || firstRowText.includes('<?php echo e(__("No bookings found")); ?>')) {
                        tbody.html(`
                            <tr>
                                <td colspan="<?php echo e(Auth::user() && Auth::user()->can('manage booking') ? '11' : '10'); ?>" class="text-center"><?php echo e(__('No canceled appointments found')); ?></td>
                            </tr>
                        `);
                    } else {
                        $bookingRows.each(function() {
                            tbody.append($(this));
                        });
                    }
                } else {
                    tbody.html(`
                        <tr>
                            <td colspan="<?php echo e(Auth::user() && Auth::user()->can('manage booking') ? '11' : '10'); ?>" class="text-center"><?php echo e(__('No canceled appointments found')); ?></td>
                        </tr>
                    `);
                }
            } catch (error) {
                console.error('Error parsing canceled bookings:', error);
                tbody.html(`
                    <tr>
                        <td colspan="<?php echo e(Auth::user() && Auth::user()->can('manage booking') ? '11' : '10'); ?>" class="text-center text-danger"><?php echo e(__('Error loading canceled appointments')); ?></td>
                    </tr>
                `);
            }
        },
        error: function(xhr) {
            console.error('Error loading canceled appointments:', xhr);
            tbody.html(`
                <tr>
                    <td colspan="<?php echo e(Auth::user() && Auth::user()->can('manage booking') ? '11' : '10'); ?>" class="text-center text-danger"><?php echo e(__('Error loading canceled appointments')); ?></td>
                </tr>
            `);
        }
    });
}

// Make functions global
window.switchAppointmentView = switchAppointmentView;

//appointment ends


let calendar;
let editingEventId = null;
let allEvents = [];

// Function to load all events and store them
function loadAllEvents() {
    $.ajax({
        url: '<?php echo e(route("calendar-events.index")); ?>',
        method: 'GET',
        success: function(response) {
            if (response.success && response.data) {
                allEvents = response.data;
                console.log('All events loaded:', allEvents);
                
                // Show today's events by default
                const today = new Date();
                const dayName = today.toLocaleDateString('en-US', { weekday: 'long' });
                updateSelectedDateContainer(today, dayName);
            }
        },
        error: function(xhr) {
            console.log('Error loading events:', xhr.responseText);
        }
    });
}

// Function to update the selected date container
function updateSelectedDateContainer(date, dayName) {
    console.log('Updating sidebar for:', dayName, date);
    
    const options = { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
    };
    
    const formattedDate = date.toLocaleDateString('en-US', options);
    
    $('#selected-date-title').text(dayName);
    $('#selected-date-subtitle').text(formattedDate);
    
    // Show events for selected date
    showEventsForDate(date);
}

// Function to show events for a specific date
function showEventsForDate(selectedDate) {
    console.log('Showing events for:', selectedDate);
    
    const dateStr = selectedDate.toISOString().split('T')[0];
    
    // Filter events for the selected date
    const dayEvents = allEvents.filter(event => {
        const eventDate = new Date(event.start_date).toISOString().split('T')[0];
        return eventDate === dateStr;
    });
    
    console.log('Found events:', dayEvents);
    
    const eventsList = $('#events-list');
    const noEventsMessage = $('#no-events-message');
    
    if (dayEvents.length > 0) {
        noEventsMessage.hide();
        eventsList.show();
        eventsList.empty();
        
        dayEvents.forEach(event => {
            const startTime = new Date(event.start_date).toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit'
            });
            
            const endTime = new Date(event.end_date).toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit'
            });
            
            // Format location display for sidebar
            let sidebarLocationDisplay = '';
            if (event.locations_data) {
                try {
                    const locations = JSON.parse(event.locations_data);
                    if (locations && locations.length > 0) {
                        if (locations.length === 1) {
                            sidebarLocationDisplay = `<div class="small text-success"><i class="ti ti-map-pin me-1"></i>${locations[0].display}</div>`;
                        } else {
                            sidebarLocationDisplay = `<div class="small text-success"><i class="ti ti-map-pin me-1"></i>${locations[0].display} <span class="badge bg-primary location-count-badge ms-1">+${locations.length - 1}</span></div>`;
                        }
                    }
                } catch (e) {
                    console.error('Error parsing locations_data for sidebar:', e);
                    sidebarLocationDisplay = event.location ? `<div class="small text-success"><i class="ti ti-map-pin me-1"></i>${event.location}</div>` : '';
                }
            } else if (event.location) {
                sidebarLocationDisplay = `<div class="small text-success"><i class="ti ti-map-pin me-1"></i>${event.location}</div>`;
            }

            const eventHtml = `
                <li class="mb-3 p-3 border rounded bg-light">
                    <h6 class="mb-1 text-primary">${event.title}</h6>
                    <div class="small text-muted mb-2">
                        <i class="ti ti-clock me-1"></i>${startTime} - ${endTime}
                    </div>
                    ${event.description ? `<p class="small mb-2">${event.description}</p>` : ''}
                    ${sidebarLocationDisplay}
                </li>
            `;
            eventsList.append(eventHtml);
        });
    } else {
        eventsList.hide();
        noEventsMessage.show();
        noEventsMessage.html(`
            <i class="ti ti-calendar-off fs-1 mb-2 d-block text-muted"></i>
            <div class="text-muted">No events scheduled for this date</div>
        `);
    }
}




// Function to collect availability data
function collectAvailabilityData() {
    const availability = {};

    // Days of the week
    const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

    days.forEach(function(day) {
        const dayCheckbox = $(`input[name="availability[${day}][enabled]"]`);
        const isEnabled = dayCheckbox.is(':checked');

        availability[day] = {
            enabled: isEnabled,
            slots: []
        };

        if (isEnabled) {
            // Collect time slots for this day
            $(`input[name^="availability[${day}][slots]"][name$="[start]"]`).each(function(index) {
                const startTime = $(this).val();
                const endTime = $(`input[name="availability[${day}][slots][${index}][end]"]`).val();

                if (startTime && endTime) {
                    availability[day].slots.push({
                        start: startTime,
                        end: endTime
                    });
                }
            });
        }
    });

    console.log('Collected availability data:', availability);
    return availability;
}

// Minimum Notice Calculation Functions
function calculateMinimumNoticeInMinutes() {
    const value = parseInt($('#minimum_notice_value').val()) || 0;
    const unit = $('#minimum_notice_unit').val();

    let minutes = 0;
    switch (unit) {
        case 'minutes':
            minutes = value;
            break;
        case 'hours':
            minutes = value * 60;
            break;
        case 'days':
            minutes = value * 60 * 24;
            break;
        case 'months':
            minutes = value * 60 * 24 * 30; // Approximate 30 days per month
            break;
        default:
            minutes = 0;
    }

    // Update the hidden field
    $('#minimum_notice').val(minutes);

    // Update the preview text
    updateMinimumNoticePreview(value, unit);

    return minutes;
}

function setMinimumNoticeFromMinutes(totalMinutes) {
    totalMinutes = parseInt(totalMinutes) || 0;

    if (totalMinutes === 0) {
        $('#minimum_notice_value').val(0);
        $('#minimum_notice_unit').val('minutes');
        updateMinimumNoticePreview(0, 'minutes');
        return;
    }

    // Convert to the most appropriate unit
    if (totalMinutes >= 43200) { // 30 days or more
        const months = Math.floor(totalMinutes / (60 * 24 * 30));
        $('#minimum_notice_value').val(months);
        $('#minimum_notice_unit').val('months');
        updateMinimumNoticePreview(months, 'months');
    } else if (totalMinutes >= 1440) { // 1 day or more
        const days = Math.floor(totalMinutes / (60 * 24));
        $('#minimum_notice_value').val(days);
        $('#minimum_notice_unit').val('days');
        updateMinimumNoticePreview(days, 'days');
    } else if (totalMinutes >= 60) { // 1 hour or more
        const hours = Math.floor(totalMinutes / 60);
        $('#minimum_notice_value').val(hours);
        $('#minimum_notice_unit').val('hours');
        updateMinimumNoticePreview(hours, 'hours');
    } else {
        $('#minimum_notice_value').val(totalMinutes);
        $('#minimum_notice_unit').val('minutes');
        updateMinimumNoticePreview(totalMinutes, 'minutes');
    }
}

function updateMinimumNoticePreview(value, unit) {
    const previewElement = $('#minimum_notice_preview');

    if (value === 0 || !value) {
        previewElement.text('No minimum notice required');
        return;
    }

    let previewText = '';
    if (value === 1) {
        // Singular form
        switch (unit) {
            case 'minutes':
                previewText = '1 minute advance notice required';
                break;
            case 'hours':
                previewText = '1 hour advance notice required';
                break;
            case 'days':
                previewText = '1 day advance notice required';
                break;
            case 'months':
                previewText = '1 month advance notice required';
                break;
        }
    } else {
        // Plural form
        previewText = `${value} ${unit} advance notice required`;
    }

    previewElement.text(previewText);
}

$(document).ready(function() {
    console.log('Document ready - initializing...');

    // Validate that required modal elements exist
    const requiredModalElements = [
        '#viewEventModal', '#view-event-title',
        '#view-event-duration', '#view-event-booking-slots',
        '#view-event-notice'
    ];

    let missingElements = [];
    requiredModalElements.forEach(elementId => {
        if ($(elementId).length === 0) {
            missingElements.push(elementId);
        }
    });

    if (missingElements.length > 0) {
        console.error('Missing required modal elements:', missingElements);
    }

    // Initialize calendar
    initializeCalendar();

    // Load all events for the sidebar
    loadAllEvents();

    // Add event listeners for minimum notice calculation
    $('#minimum_notice_value, #minimum_notice_unit').on('change input', function() {
        calculateMinimumNoticeInMinutes();
    });

    // Initialize minimum notice calculation
    calculateMinimumNoticeInMinutes();
    
    // Form submission handler
    $('#createEventForm').on('submit', function(e) {
        e.preventDefault();
        console.log('Form submitted!'); // Check if this log appears
        console.log('editingEventId at form submission:', editingEventId); // Debug log
        
        const submitBtn = $('#submitBtn');
        const originalText = submitBtn.text();
        
        // Show loading
        submitBtn.prop('disabled', true).text(editingEventId ? 'Updating...' : 'Creating...');
        
        // Collect custom fields data (field unique key, type, label and options only, no values)
        const customFields = [];

        try {
            $('#custom-fields-container .custom-field-row').each(function() {
                const fieldUniqueKey = $(this).data('field-unique-key');
                const fieldType = $(this).data('field-type');
                const fieldLabel = $(this).data('field-label');

                if (fieldUniqueKey && fieldType && fieldLabel) {
                    // Get field options from the original dropdown
                    const originalOption = $(`#custom_field option[value="${fieldUniqueKey}"]`);
                    const fieldOptions = originalOption.data('field-options') || [];

                    customFields.push({
                        unique_key: fieldUniqueKey,
                        type: fieldType,
                        label: fieldLabel,
                        options: fieldOptions
                    });
                }
            });
        } catch (error) {
            console.log('Error collecting custom fields:', error);
        }

        // Get form data
        const formData = {
            title: $('#event_title').val(),
            duration: $('#event_duration').val() || 60,
            booking_per_slot: $('#booking_per_slot').val() || 1,
            minimum_notice: $('#minimum_notice').val() || 0,
            minimum_notice_value: $('#minimum_notice_value').val() || 0,
            minimum_notice_unit: $('#minimum_notice_unit').val() || 'minutes',
            description: $('#event_description').val(),
            locations_data: $('#event_locations_data').val(),
            custom_redirect_url: $('#custom_redirect_url').val(),
            assigned_staff_id: $('#assigned_staff_id').val() || null,
            // Legacy fields for backward compatibility
            location: selectedLocations.length > 0 ? selectedLocations[0].type : '',
            meet_link: selectedLocations.find(loc => ['zoom', 'meet', 'skype', 'phone', 'others'].includes(loc.type))?.value || '',
            physical_address: selectedLocations.find(loc => loc.type === 'in_person')?.value || '',
            require_name: $('#require_name').is(':checked'),
            require_email: $('#require_email').is(':checked'),
            require_phone: $('#require_phone').is(':checked'),
            custom_field: $('#custom_field').val(), // Single field for backward compatibility
            custom_fields: customFields, // Multiple field names array (no values)
            // Date Range fields
            date_range_type: $('input[name="date_range_type"]:checked').val() || 'indefinitely',
            date_range_days: $('#calendar_days_input').val() || null,
            date_range_start: $('#date_range_start_input').val() || null,
            date_range_end: $('#date_range_end_input').val() || null,
            // Status field
            status: $('#event_status').val() || 'active',
            availability: (function() {
                try {
                    return collectAvailabilityData();
                } catch (error) {
                    console.log('Error collecting availability data:', error);
                    return {};
                }
            })(),
            date_override: (function() {
                try {
                    return collectDateOverrideData();
                } catch (error) {
                    console.log('Error collecting date override data:', error);
                    return {};
                }
            })(),
            _token: $('input[name="_token"]').val()
        };

        // Add _method field for PUT request when updating
        if (editingEventId) {
            formData._method = 'PUT';
        }

        console.log('Sending data:', formData); // Log form data
        console.log('Date Range Type:', formData.date_range_type);
        console.log('Date Range Days:', formData.date_range_days);
        console.log('Date Range Start:', formData.date_range_start);
        console.log('Date Range End:', formData.date_range_end);

        // Determine URL - always use POST but add _method for PUT
        const url = editingEventId ?
            `<?php echo e(url('calendar-events')); ?>/${editingEventId}` :
            '<?php echo e(route("calendar-events.store")); ?>';

        console.log('AJAX URL:', url); // Log the URL
        console.log('Editing Event ID:', editingEventId); // Log editing state
        
        // Send AJAX request
        $.ajax({
            url: url,
            method: 'POST',
            data: formData,
            success: function(response) {
                console.log('Success response:', response);
                
                // Reset button
                submitBtn.prop('disabled', false).text(originalText);
                
                if (response.success) {
                    // Close modal
                    $('#createEventModal').modal('hide');
                    
                    // Reset form
                    resetForm();
                    
                    // Show success toast message
                    console.log('editingEventId at success:', editingEventId); // Debug log
                    show_toastr('success', editingEventId ? 'Event updated successfully!' : 'Event created successfully!');
                    
                    // Reset editing state
                    editingEventId = null;
                    
                    // Refresh calendar and events
                    if (calendar) {
                        calendar.refetchEvents();
                    }
                    loadEventsList(currentPage, currentPerPage);
                    loadAllEvents(); // Refresh sidebar events
                } else {
                    console.log('Validation errors:', response.errors); // Log validation errors
                    show_toastr('error', response.message || 'Unknown error');
                }
            },
            error: function(xhr, status, error) {
                console.log('Error response:', xhr.responseText);
                
                // Reset button
                submitBtn.prop('disabled', false).text(originalText);
                
                let errorMessage = 'An error occurred';
                
                try {
                    const errorResponse = JSON.parse(xhr.responseText);
                    if (errorResponse.errors) {
                        errorMessage = 'Validation errors:\n';
                        Object.keys(errorResponse.errors).forEach(key => {
                            errorMessage += `${key}: ${errorResponse.errors[key].join(', ')}\n`;
                        });
                    } else if (errorResponse.message) {
                        errorMessage = errorResponse.message;
                    }
                } catch (e) {
                    errorMessage = `Server Error (${xhr.status}): ${xhr.responseText || 'Unknown error'}`;
                }
                
                show_toastr('error', errorMessage);
            }
        });
    });
});
   

// Initialize calendar
function initializeCalendar() {
    console.log('Initializing calendar...');
    
    if (typeof FullCalendar === 'undefined') {
        console.error('FullCalendar not loaded');
        return;
    }
    
    const calendarEl = document.getElementById('calendar');
    if (!calendarEl) {
        console.error('Calendar element not found');
        return;
    }
    
    try {
        calendar = new FullCalendar.Calendar(calendarEl, {
            initialView: 'dayGridMonth',
            headerToolbar: {
                left: 'prev,next today',
                center: 'title',
                right: 'dayGridMonth,timeGridWeek,timeGridDay'
            },
            buttonText: {
                today: "<?php echo e(__('Today')); ?>",
                month: "<?php echo e(__('Month')); ?>",
                week: "<?php echo e(__('Week')); ?>",
                day: "<?php echo e(__('Day')); ?>"
            },
            height: 'auto',
            selectable: false,
            selectMirror: false,
            events: function(fetchInfo, successCallback, failureCallback) {
                $.ajax({
                    url: "<?php echo e(route('calendar-events.calendar-data')); ?>",
                    method: "POST",
                    data: {
                        "_token": "<?php echo e(csrf_token()); ?>"
                    },
                    success: function(data) {
                        console.log('Calendar data loaded:', data);
                        successCallback(data);
                    },
                    error: function(xhr, status, error) {
                        console.log('Error loading calendar data:', error);
                        failureCallback(error);
                    }
                });
            },
            // select: function(info) {
            //     // This function is disabled to prevent interference with dateClick
            //     // If you need to create events, use the "Create Event" button instead
            // },
            eventClick: function(info) {
                // Add these lines:
                if (info.jsEvent) {
                    info.jsEvent.stopPropagation();
                    info.jsEvent.preventDefault();
                }
                // ...existing code...
                if (!info || !info.event) {
                    console.warn('Invalid event click info:', info);
                    return;
                }

                const eventId = info.event.id;
                console.log('Calendar event clicked, raw ID:', eventId);

                if (eventId) {
                    // Check if this is an appointment booking (different handling)
                    if (typeof eventId === 'string' && eventId.startsWith('appointment_')) {
                        console.log('Appointment booking clicked, not handling in viewEvent');
                        return;
                    }

                    // Extract numeric ID from prefixed ID (e.g., 'event_123' -> '123')
                    let numericId = eventId;
                    if (typeof eventId === 'string' && eventId.startsWith('event_')) {
                        numericId = eventId.replace('event_', '');
                        console.log('Extracted numeric ID from prefixed calendar event:', numericId);
                    }

                    // Convert to integer to ensure consistency
                    const finalId = parseInt(numericId);
                    if (isNaN(finalId)) {
                        console.error('Could not extract valid numeric ID from:', eventId);
                        return;
                    }

                    console.log('Calling viewEvent with numeric ID:', finalId);
                    viewEvent(finalId);
                }
            },
            dateClick: function(info) {
                console.log('Date clicked:', info);

                const clickedDate = new Date(info.date);
                const formattedDate = clickedDate.toISOString().split('T')[0];

                // Set the selected date in the appointment form
                // Load available events
                loadAvailableEvents();

                // Show the appointment booking modal
                $('#appointmentBookingModal').modal('show');
            }
        });
        
        calendar.render();
        console.log('Calendar initialized successfully');
        
    } catch (error) {
        console.error('Error initializing calendar:', error);
    }
}


//custom field
let customFieldCounter = 0;

function toggleCustomField() {
    // This function is now just for compatibility
    // The actual functionality is handled by addCustomField()
}

function addCustomField() {
    const selectedOption = $('#custom_field option:selected');
    const fieldValue = selectedOption.val();
    const fieldLabel = selectedOption.text();
    const fieldType = selectedOption.data('field-type');
    const fieldOptions = selectedOption.data('field-options') || [];

    // Don't add if no field is selected
    if (!fieldValue) {
        alert('Please select a field type first');
        return;
    }

    // Check if this field already exists
    if ($(`#custom-field-${fieldValue}`).length > 0) {
        alert('This field has already been added');
        return;
    }

    customFieldCounter++;
    const fieldId = `custom-field-${fieldValue}`;

    let inputHtml = '';

    // Generate appropriate input based on field type from settings (DISABLED for event creation)
    switch(fieldType) {
        case 'date':
            inputHtml = `<input type="date" class="form-control" id="${fieldId}" name="custom_fields[${fieldValue}]" placeholder="Select date" disabled>`;
            break;
        case 'datetime':
            inputHtml = `<input type="datetime-local" class="form-control" id="${fieldId}" name="custom_fields[${fieldValue}]" placeholder="Select date and time" disabled>`;
            break;
        case 'number':
            inputHtml = `<input type="number" class="form-control" id="${fieldId}" name="custom_fields[${fieldValue}]" placeholder="Enter number" disabled>`;
            break;
        case 'email':
            inputHtml = `<input type="email" class="form-control" id="${fieldId}" name="custom_fields[${fieldValue}]" placeholder="Enter email" disabled>`;
            break;
        case 'textarea':
            inputHtml = `<textarea class="form-control" id="${fieldId}" name="custom_fields[${fieldValue}]" rows="3" placeholder="Enter details" disabled></textarea>`;
            break;
        case 'select':
            inputHtml = `<select class="form-control" id="${fieldId}" name="custom_fields[${fieldValue}]" disabled>
                <option value="">Select an option</option>`;
            if (Array.isArray(fieldOptions)) {
                fieldOptions.forEach(option => {
                    inputHtml += `<option value="${option}">${option}</option>`;
                });
            }
            inputHtml += `</select>`;
            break;
        case 'radio':
            inputHtml = '<div class="radio-group">';
            if (Array.isArray(fieldOptions)) {
                fieldOptions.forEach((option, index) => {
                    inputHtml += `
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="custom_fields[${fieldValue}]" id="${fieldId}_${index}" value="${option}" disabled>
                            <label class="form-check-label" for="${fieldId}_${index}">${option}</label>
                        </div>`;
                });
            }
            inputHtml += '</div>';
            break;
        case 'checkbox':
            inputHtml = '<div class="checkbox-group">';
            if (Array.isArray(fieldOptions)) {
                fieldOptions.forEach((option, index) => {
                    inputHtml += `
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="custom_fields[${fieldValue}][]" id="${fieldId}_${index}" value="${option}" disabled>
                            <label class="form-check-label" for="${fieldId}_${index}">${option}</label>
                        </div>`;
                });
            }
            inputHtml += '</div>';
            break;
        case 'multiselect':
            inputHtml = `<select class="form-control" id="${fieldId}" name="custom_fields[${fieldValue}][]" multiple disabled>`;
            if (Array.isArray(fieldOptions)) {
                fieldOptions.forEach(option => {
                    inputHtml += `<option value="${option}">${option}</option>`;
                });
            }
            inputHtml += `</select>`;
            break;
        case 'file':
            inputHtml = `<input type="file" class="form-control" id="${fieldId}" name="custom_fields[${fieldValue}]" disabled>`;
            break;
        case 'file_multiple':
            inputHtml = `<input type="file" class="form-control" id="${fieldId}" name="custom_fields[${fieldValue}][]" multiple disabled>`;
            break;
        case 'color':
            inputHtml = `<input type="color" class="form-control" id="${fieldId}" name="custom_fields[${fieldValue}]" disabled>`;
            break;
        case 'link':
            inputHtml = `<input type="url" class="form-control" id="${fieldId}" name="custom_fields[${fieldValue}]" placeholder="Enter URL" disabled>`;
            break;
        default:
            inputHtml = `<input type="text" class="form-control" id="${fieldId}" name="custom_fields[${fieldValue}]" placeholder="Enter ${fieldLabel.toLowerCase()}" disabled>`;
    }

    // Create the field HTML
    const fieldHtml = `
        <div class="row mb-3 custom-field-row" data-field="${fieldValue}" data-field-type="${fieldType}" data-field-label="${fieldLabel}" data-field-unique-key="${fieldValue}">
            <div class="col-10">
                <label for="${fieldId}" class="form-label">${fieldLabel}</label>
                ${inputHtml}
            </div>
            <div class="col-2 d-flex align-items-end">
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeCustomField('${fieldValue}')">
                    <i class="ti ti-trash"></i>
                </button>
            </div>
        </div>
    `;

    // Add the field to the container
    $('#custom-fields-container').append(fieldHtml);

    // Reset the dropdown
    $('#custom_field').val('');
}

function removeCustomField(fieldValue) {
    $(`.custom-field-row[data-field="${fieldValue}"]`).remove();
}

// Function to populate custom fields when editing an event
function populateCustomFieldsForEdit(event) {
    // Clear existing custom fields
    $('#custom-fields-container').empty();
    customFieldCounter = 0;

    // Check if event has custom fields
    if (event.custom_fields && Array.isArray(event.custom_fields)) {
        event.custom_fields.forEach(function(field) {
            if (field.unique_key && field.label) {
                // Add the custom field to the form
                addCustomFieldForEdit(field.unique_key, field.label, field.type, field.options);
            } else if (field.type && field.label) {
                // Backward compatibility for old format
                addCustomFieldForEdit(field.type, field.label, field.type, field.options);
            }
        });
    }
}

// Function to add a custom field during edit (similar to addCustomField but for editing)
function addCustomFieldForEdit(fieldUniqueKey, fieldLabel, fieldType, fieldOptions) {
    // Check if this field already exists
    if ($(`#custom-field-${fieldUniqueKey}`).length > 0) {
        return; // Field already exists
    }

    customFieldCounter++;
    const fieldId = `custom-field-${fieldUniqueKey}`;

    let inputHtml = '';

    // Generate appropriate input based on field type from settings (DISABLED for event creation)
    switch(fieldType) {
        case 'date':
            inputHtml = `<input type="date" class="form-control" id="${fieldId}" name="custom_fields[${fieldUniqueKey}]" placeholder="Select date" disabled>`;
            break;
        case 'datetime':
            inputHtml = `<input type="datetime-local" class="form-control" id="${fieldId}" name="custom_fields[${fieldUniqueKey}]" placeholder="Select date and time" disabled>`;
            break;
        case 'number':
            inputHtml = `<input type="number" class="form-control" id="${fieldId}" name="custom_fields[${fieldUniqueKey}]" placeholder="Enter number" disabled>`;
            break;
        case 'email':
            inputHtml = `<input type="email" class="form-control" id="${fieldId}" name="custom_fields[${fieldUniqueKey}]" placeholder="Enter email" disabled>`;
            break;
        case 'textarea':
            inputHtml = `<textarea class="form-control" id="${fieldId}" name="custom_fields[${fieldUniqueKey}]" rows="3" placeholder="Enter details" disabled></textarea>`;
            break;
        case 'select':
            inputHtml = `<select class="form-control" id="${fieldId}" name="custom_fields[${fieldUniqueKey}]" disabled>
                <option value="">Select an option</option>`;
            if (Array.isArray(fieldOptions)) {
                fieldOptions.forEach(option => {
                    inputHtml += `<option value="${option}">${option}</option>`;
                });
            }
            inputHtml += `</select>`;
            break;
        case 'radio':
            inputHtml = '<div class="radio-group">';
            if (Array.isArray(fieldOptions)) {
                fieldOptions.forEach((option, index) => {
                    inputHtml += `
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="custom_fields[${fieldUniqueKey}]" id="${fieldId}_${index}" value="${option}" disabled>
                            <label class="form-check-label" for="${fieldId}_${index}">${option}</label>
                        </div>`;
                });
            }
            inputHtml += '</div>';
            break;
        case 'checkbox':
            inputHtml = '<div class="checkbox-group">';
            if (Array.isArray(fieldOptions)) {
                fieldOptions.forEach((option, index) => {
                    inputHtml += `
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="custom_fields[${fieldUniqueKey}][]" id="${fieldId}_${index}" value="${option}" disabled>
                            <label class="form-check-label" for="${fieldId}_${index}">${option}</label>
                        </div>`;
                });
            }
            inputHtml += '</div>';
            break;
        case 'multiselect':
            inputHtml = `<select class="form-control" id="${fieldId}" name="custom_fields[${fieldUniqueKey}][]" multiple disabled>`;
            if (Array.isArray(fieldOptions)) {
                fieldOptions.forEach(option => {
                    inputHtml += `<option value="${option}">${option}</option>`;
                });
            }
            inputHtml += `</select>`;
            break;
        case 'file':
            inputHtml = `<input type="file" class="form-control" id="${fieldId}" name="custom_fields[${fieldUniqueKey}]" disabled>`;
            break;
        case 'file_multiple':
            inputHtml = `<input type="file" class="form-control" id="${fieldId}" name="custom_fields[${fieldUniqueKey}][]" multiple disabled>`;
            break;
        case 'color':
            inputHtml = `<input type="color" class="form-control" id="${fieldId}" name="custom_fields[${fieldUniqueKey}]" disabled>`;
            break;
        case 'link':
            inputHtml = `<input type="url" class="form-control" id="${fieldId}" name="custom_fields[${fieldUniqueKey}]" placeholder="Enter URL" disabled>`;
            break;
        default:
            inputHtml = `<input type="text" class="form-control" id="${fieldId}" name="custom_fields[${fieldUniqueKey}]" placeholder="Enter ${fieldLabel.toLowerCase()}" disabled>`;
    }

    // Create the field HTML
    const fieldHtml = `
        <div class="row mb-3 custom-field-row" data-field="${fieldUniqueKey}" data-field-type="${fieldType}" data-field-label="${fieldLabel}" data-field-unique-key="${fieldUniqueKey}">
            <div class="col-10">
                <label for="${fieldId}" class="form-label">${fieldLabel}</label>
                ${inputHtml}
            </div>
            <div class="col-2 d-flex align-items-end">
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeCustomField('${fieldUniqueKey}')">
                    <i class="ti ti-trash"></i>
                </button>
            </div>
        </div>
    `;

    // Add the field to the container
    $('#custom-fields-container').append(fieldHtml);
}

// Appointment Booking Functions
function loadAvailableEvents() {
    console.log('Loading available events');

    // Clear previous options
    $('#appointment_calendar_event').html('<option value=""><?php echo e(__("Select an event")); ?></option>');
    $('#appointment_timeslot').html('<option value=""><?php echo e(__("Select a time slot")); ?></option>');
    $('#appointment_location_type').val('');
    $('#appointment_location_value').val('');

    // Make AJAX call to get all available events
    $.ajax({
        url: '<?php echo e(route("calendar-events.index")); ?>',
        method: 'GET',
        success: function(response) {
            if (response.success && response.data.length > 0) {
                // Store events globally for time slot generation
                window.availableEvents = response.data;

                response.data.forEach(function(event) {
                    // Add null check for event and required properties
                    if (!event || !event.id || !event.title) {
                        console.warn('Skipping invalid event in appointment dropdown:', event);
                        return;
                    }

                    // Determine location value based on location type
                    let locationValue = '';
                    if (event.location === 'in_person') {
                        locationValue = event.physical_address || 'Physical address not specified';
                    } else {
                        locationValue = event.meet_link || 'Meeting link not specified';
                    }

                    $('#appointment_calendar_event').append(
                        `<option value="${event.id}" data-location="${event.location || ''}" data-location-value="${locationValue}" data-duration="${event.duration || 60}">
                            ${event.title} (${event.duration || 60} min)
                        </option>`
                    );
                });
            } else {
                window.availableEvents = [];
                $('#appointment_calendar_event').append('<option value="" disabled><?php echo e(__("No events available for this date")); ?></option>');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading events:', error);
            window.availableEvents = [];
            alert('Error loading events for the selected date');
        }
    });
}

function updateEventDetails() {
    const selectedOption = $('#appointment_calendar_event option:selected');
    const eventId = selectedOption.val();

    if (eventId) {
        // Get event data from the global events array
        const selectedEvent = window.availableEvents?.find(event => event.id == eventId);

        if (selectedEvent) {
            // Clear and populate location dropdown
            $('#appointment_location_type').html('<option value=""><?php echo e(__("Select a location")); ?></option>');

            // Store the selected event data globally for location updates
            window.selectedEventData = selectedEvent;

            if (selectedEvent.locations_data) {
                // New multi-location system
                try {
                    const locations = JSON.parse(selectedEvent.locations_data);
                    if (locations && locations.length > 0) {
                        // Populate location dropdown with all available locations
                        locations.forEach(function(location, index) {
                            $('#appointment_location_type').append(
                                `<option value="${index}" data-location-value="${location.value || 'Not specified'}" data-location-display="${location.display}">
                                    ${location.display}
                                </option>`
                            );
                        });

                        // Auto-select first location and update value
                        $('#appointment_location_type').val('0');
                        $('#appointment_location_value').val(locations[0].value || 'Not specified');
                    }
                } catch (e) {
                    console.error('Error parsing locations_data:', e);
                    $('#appointment_location_type').append('<option value="error">Location data error</option>');
                    $('#appointment_location_value').val('Location data error');
                }
            } else {
                // Legacy single location system
                const location = selectedEvent.location;
                const locationDisplay = getLocationDisplayName(location);
                let locationValue = '';

                if (location === 'in_person') {
                    locationValue = selectedEvent.physical_address || 'Physical address not specified';
                } else {
                    locationValue = selectedEvent.meet_link || 'Meeting link not specified';
                }

                // Add single location option
                $('#appointment_location_type').append(
                    `<option value="0" data-location-value="${locationValue}" data-location-display="${locationDisplay}">
                        ${locationDisplay}
                    </option>`
                );

                // Auto-select and update value
                $('#appointment_location_type').val('0');
                $('#appointment_location_value').val(locationValue);
            }

            // Set the event date from the calendar event's start date
            if (selectedEvent.start_date) {
                const eventDate = new Date(selectedEvent.start_date);
                const formattedDate = eventDate.toISOString().split('T')[0]; // YYYY-MM-DD format
                $('#appointment_date').val(formattedDate);
            }
        } else {
            // Fallback to data attributes if global events not available
            const location = selectedOption.data('location');
            const locationValue = selectedOption.data('location-value');
            const locationDisplay = getLocationDisplayName(location);

            $('#appointment_location_type').html('<option value=""><?php echo e(__("Select a location")); ?></option>');
            $('#appointment_location_type').append(
                `<option value="0" data-location-value="${locationValue || 'Not specified'}" data-location-display="${locationDisplay}">
                    ${locationDisplay}
                </option>`
            );
            $('#appointment_location_type').val('0');
            $('#appointment_location_value').val(locationValue || 'Not specified');

            // Set current date as fallback
            $('#appointment_date').val(new Date().toISOString().split('T')[0]);
        }

        // Generate time slots for the selected event
        generateSimpleTimeSlots();
    } else {
        // Clear fields if no event selected
        $('#appointment_location_type').html('<option value=""><?php echo e(__("Select a location")); ?></option>');
        $('#appointment_location_value').val('');
        $('#appointment_date').val('');
        $('#appointment_timeslot').html('<option value=""><?php echo e(__("Select a time slot")); ?></option>');
        window.selectedEventData = null;
    }
}

function updateLocationValue() {
    const selectedOption = $('#appointment_location_type option:selected');
    const locationValue = selectedOption.data('location-value');

    if (locationValue) {
        $('#appointment_location_value').val(locationValue);
    } else {
        $('#appointment_location_value').val('');
    }
}

function getLocationDisplayName(location) {
    const locationNames = {
        'in_person': 'In Person',
        'zoom': 'Zoom',
        'skype': 'Skype',
        'meet': 'Google Meet',
        'phone': 'Phone Call',
        'others': 'Other'
    };
    return locationNames[location] || location;
}

function generateTimeSlots(eventId, duration) {
    console.log('Generating time slots for event:', eventId);

    // Generate time slots based on booking table data
    generateTimeSlotsFromBookingData(eventId);
}

function generateSimpleTimeSlots() {
    console.log('Generating simple time slots');
    $('#appointment_timeslot').html('<option value=""><?php echo e(__("Select a time slot")); ?></option>');

    // Start from 10:00 AM and go to 5:00 PM in 30-minute intervals
    let currentHour = 10;
    let currentMinute = 0;
    const endHour = 17;

    while (currentHour < endHour || (currentHour === endHour && currentMinute === 0)) {
        const timeString = `${currentHour.toString().padStart(2, '0')}:${currentMinute.toString().padStart(2, '0')}`;
        const displayTime = formatTime12Hour(timeString);

        console.log('Adding simple time slot:', timeString, '->', displayTime);
        $('#appointment_timeslot').append(`<option value="${timeString}">${displayTime}</option>`);

        // Add 30 minutes
        currentMinute += 30;
        if (currentMinute >= 60) {
            currentMinute = 0;
            currentHour += 1;
        }
    }

    console.log('Simple time slots generation completed');
}

function generateTimeSlotsFromBookingData(eventId) {
    console.log('Getting booking data for event:', eventId);

    // Get booking time from the booking table for this event
    $.ajax({
        url: '<?php echo e(route("calendar-events.index")); ?>',
        method: 'GET',
        success: function(response) {
            console.log('Events response:', response);
            $('#appointment_timeslot').html('<option value=""><?php echo e(__("Select a time slot")); ?></option>');

            if (response.success && response.data) {
                // Find the selected event and get its booking time
                const selectedEvent = response.data.find(event => event.id == eventId);
                console.log('Selected event:', selectedEvent);

                let bookingStartTime = '10:00'; // Default start time

                if (selectedEvent && selectedEvent.bookings && selectedEvent.bookings.length > 0) {
                    // Get the time from the first booking for this event
                    bookingStartTime = selectedEvent.bookings[0].time;
                    console.log('Found booking start time:', bookingStartTime);
                } else {
                    console.log('No bookings found, using default time:', bookingStartTime);
                }

                // Parse start time from booking
                const [startHour, startMinute] = bookingStartTime.split(':').map(Number);
                console.log('Start hour:', startHour, 'Start minute:', startMinute);

                // End time is 5:00 PM (17:00)
                const endHour = 17;
                const endMinute = 0;

                // Generate time slots in 30-minute intervals
                let currentHour = startHour;
                let currentMinute = startMinute;

                console.log('Generating time slots from', currentHour + ':' + currentMinute, 'to 17:00');

                while (currentHour < endHour || (currentHour === endHour && currentMinute <= endMinute)) {
                    const timeString = `${currentHour.toString().padStart(2, '0')}:${currentMinute.toString().padStart(2, '0')}`;
                    const displayTime = formatTime12Hour(timeString);

                    console.log('Adding time slot:', timeString, '->', displayTime);
                    $('#appointment_timeslot').append(`<option value="${timeString}">${displayTime}</option>`);

                    // Add 30 minutes
                    currentMinute += 30;
                    if (currentMinute >= 60) {
                        currentMinute = 0;
                        currentHour += 1;
                    }

                    // Stop if we've reached past 5:00 PM
                    if (currentHour > endHour || (currentHour === endHour && currentMinute > endMinute)) {
                        break;
                    }
                }

                console.log('Time slots generation completed');
            } else {
                console.log('No event data found, using fallback');
                // Final fallback
                generateFallbackTimeSlots();
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading booking data:', error);
            // Final fallback
            generateFallbackTimeSlots();
        }
    });
}

function generateDefaultTimeSlots(duration = 60) {
    $('#appointment_timeslot').html('<option value=""><?php echo e(__("Select a time slot")); ?></option>');

    const selectedEventId = $('#appointment_calendar_event').val();

    if (!selectedEventId) {
        return;
    }

    // Get booking time from the booking table for this event
    $.ajax({
        url: '<?php echo e(route("calendar-events.index")); ?>',
        method: 'GET',
        success: function(response) {
            if (response.success && response.data) {
                // Find the selected event and get its booking time
                const selectedEvent = response.data.find(event => event.id == selectedEventId);
                let bookingStartTime = '09:00'; // Default start time

                if (selectedEvent && selectedEvent.bookings && selectedEvent.bookings.length > 0) {
                    // Get the time from the first booking (or you can modify this logic)
                    bookingStartTime = selectedEvent.bookings[0].time;
                }

                // Parse start time from booking
                const [startHour, startMinute] = bookingStartTime.split(':').map(Number);

                // End time is 5:00 PM (17:00)
                const endHour = 17;
                const endMinute = 0;

                // Generate time slots in 30-minute intervals
                let currentHour = startHour;
                let currentMinute = startMinute;

                while (currentHour < endHour || (currentHour === endHour && currentMinute <= endMinute)) {
                    const timeString = `${currentHour.toString().padStart(2, '0')}:${currentMinute.toString().padStart(2, '0')}`;
                    const displayTime = formatTime12Hour(timeString);

                    $('#appointment_timeslot').append(`<option value="${timeString}">${displayTime}</option>`);

                    // Add 30 minutes
                    currentMinute += 30;
                    if (currentMinute >= 60) {
                        currentMinute = 0;
                        currentHour += 1;
                    }

                    // Stop if we've reached past 5:00 PM
                    if (currentHour > endHour || (currentHour === endHour && currentMinute > endMinute)) {
                        break;
                    }
                }
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading booking times:', error);
            // Fallback to default 9 AM to 5 PM slots
            generateFallbackTimeSlots();
        }
    });
}

function generateFallbackTimeSlots() {
    console.log('Generating fallback time slots');
    $('#appointment_timeslot').html('<option value=""><?php echo e(__("Select a time slot")); ?></option>');

    // Generate default time slots from 10 AM to 5 PM in 30-minute intervals
    let currentHour = 10;
    let currentMinute = 0;
    const endHour = 17;
    const endMinute = 0;

    console.log('Fallback: generating from 10:00 to 17:00');

    while (currentHour < endHour || (currentHour === endHour && currentMinute <= endMinute)) {
        const timeString = `${currentHour.toString().padStart(2, '0')}:${currentMinute.toString().padStart(2, '0')}`;
        const displayTime = formatTime12Hour(timeString);

        console.log('Fallback: adding time slot:', timeString, '->', displayTime);
        $('#appointment_timeslot').append(`<option value="${timeString}">${displayTime}</option>`);

        // Add 30 minutes
        currentMinute += 30;
        if (currentMinute >= 60) {
            currentMinute = 0;
            currentHour += 1;
        }

        // Stop if we've reached past 5:00 PM
        if (currentHour > endHour || (currentHour === endHour && currentMinute > endMinute)) {
            break;
        }
    }

    console.log('Fallback time slots generation completed');
}

function formatTime12Hour(time24) {
    const [hours, minutes] = time24.split(':');
    const hour12 = hours % 12 || 12;
    const ampm = hours < 12 ? 'AM' : 'PM';
    return `${hour12}:${minutes} ${ampm}`;
}

// Populate timezone dropdown with GMT as default
function populateTimezones() {
    const timezoneSelect = $('#appointment_timezone');

    // Clear existing options except the first one
    timezoneSelect.find('option:not(:first)').remove();

    // Add GMT as the primary option (selected by default)
    timezoneSelect.append(`<option value="GMT" selected>GMT (Greenwich Mean Time) (UTC+00:00)</option>`);

    // Add separator
    timezoneSelect.append('<option disabled>──────────────────────</option>');

    // Add common timezones
    const commonTimezones = [
        { value: 'UTC', display: 'UTC (Coordinated Universal Time) (UTC+00:00)' },
        { value: 'Europe/London', display: 'London (UTC+00:00/+01:00)' },
        { value: 'America/New_York', display: 'New York (UTC-05:00/-04:00)' },
        { value: 'America/Los_Angeles', display: 'Los Angeles (UTC-08:00/-07:00)' },
        { value: 'Europe/Paris', display: 'Paris (UTC+01:00/+02:00)' },
        { value: 'Asia/Tokyo', display: 'Tokyo (UTC+09:00)' },
        { value: 'Asia/Dubai', display: 'Dubai (UTC+04:00)' },
        { value: 'Asia/Kolkata', display: 'India (UTC+05:30)' },
        { value: 'Australia/Sydney', display: 'Sydney (UTC+10:00/+11:00)' }
    ];

    // Add common timezones group
    timezoneSelect.append('<optgroup label="Common Timezones">');
    commonTimezones.forEach(tz => {
        timezoneSelect.append(`<option value="${tz.value}">${tz.display}</option>`);
    });
    timezoneSelect.append('</optgroup>');

    // Add all other timezones
    const timezones = Intl.supportedValuesOf('timeZone');
    const groupedTimezones = {};

    timezones.forEach(timezone => {
        // Skip if already in common timezones
        if (commonTimezones.some(ct => ct.value === timezone) || timezone === 'GMT') return;

        const parts = timezone.split('/');
        const region = parts[0];
        const city = parts.slice(1).join('/');

        if (!groupedTimezones[region]) {
            groupedTimezones[region] = [];
        }

        const offset = getTimezoneOffset(timezone);
        // Format timezone display name
        let displayName = city.replace(/_/g, ' ');

        // Special formatting for common locations
        if (timezone === 'Asia/Kolkata') {
            displayName = 'India';
        } else if (timezone === 'Asia/Shanghai') {
            displayName = 'China';
        } else if (timezone === 'Europe/London') {
            displayName = 'United Kingdom';
        }

        groupedTimezones[region].push({
            value: timezone,
            display: `${displayName} ${offset}`,
            offset: offset
        });
    });

    // Sort regions and add to select
    Object.keys(groupedTimezones).sort().forEach(region => {
        // Sort cities within region by offset
        groupedTimezones[region].sort((a, b) => {
            const offsetA = parseFloat(a.offset.replace(/[^\d.-]/g, ''));
            const offsetB = parseFloat(b.offset.replace(/[^\d.-]/g, ''));
            return offsetA - offsetB;
        });

        // Add region header
        timezoneSelect.append(`<optgroup label="${region}">`);

        // Add cities in this region
        groupedTimezones[region].forEach(tz => {
            timezoneSelect.append(`<option value="${tz.value}">${tz.display}</option>`);
        });

        timezoneSelect.append('</optgroup>');
    });
}

// Helper function to get timezone offset
function getTimezoneOffset(timezone) {
    const now = new Date();
    const utc = new Date(now.getTime() + (now.getTimezoneOffset() * 60000));
    const targetTime = new Date(utc.toLocaleString("en-US", {timeZone: timezone}));
    const diff = targetTime.getTime() - utc.getTime();
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    const sign = hours >= 0 ? '+' : '-';
    const absHours = Math.abs(hours);
    const absMinutes = Math.abs(minutes);

    return `(UTC${sign}${absHours.toString().padStart(2, '0')}:${absMinutes.toString().padStart(2, '0')})`;
}

// Handle appointment booking form submission
$(document).ready(function() {
    // Populate timezones when document is ready
    populateTimezones();

    $('#appointmentBookingForm').on('submit', function(e) {
        e.preventDefault();

        const formData = {
            event_id: $('#appointment_calendar_event').val(),
            event_location: $('#appointment_location_type').val(),
            event_location_value: $('#appointment_location_value').val(),
            event_date: $('#appointment_date').val(), // Event's start date
            time_zone: $('#appointment_timezone').val(),
            time_slots: $('#appointment_timeslot').val(),
            _token: $('input[name="_token"]').val()
        };

        // Validate required fields
        if (!formData.event_id || !formData.event_location || !formData.event_date || !formData.time_slots || !formData.time_zone) {
            alert('Please fill in all required fields');
            return;
        }

        // Disable submit button
        $('#appointmentSubmitBtn').prop('disabled', true).html('<i class="ti ti-loader me-1"></i><?php echo e(__("Booking...")); ?>');

        // Submit appointment booking
        $.ajax({
            url: '<?php echo e(route("appointment-bookings.store")); ?>',
            method: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    show_toastr('success', 'Appointment booked successfully!');
                    $('#appointmentBookingModal').modal('hide');
                    resetAppointmentForm();

                    // Refresh calendar to show the new appointment
                    if (typeof calendar !== 'undefined') {
                        calendar.refetchEvents();
                    }
                } else {
                    show_toastr('error', response.message || 'Failed to book appointment');
                }
            },
            error: function(xhr, status, error) {
                console.error('Error booking appointment:', error);
                let errorMessage = 'Error booking appointment';

                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                    const errors = Object.values(xhr.responseJSON.errors).flat();
                    errorMessage = errors.join(', ');
                }

                show_toastr('error', errorMessage);
            },
            complete: function() {
                // Re-enable submit button
                $('#appointmentSubmitBtn').prop('disabled', false).html('<i class="ti ti-check me-1"></i><?php echo e(__("Book Appointment")); ?>');
            }
        });
    });
});

function resetAppointmentForm() {
    $('#appointmentBookingForm')[0].reset();
    $('#appointment_calendar_event').html('<option value=""><?php echo e(__("Select an event")); ?></option>');
    $('#appointment_timeslot').html('<option value=""><?php echo e(__("Select a time slot")); ?></option>');
    $('#appointment_location_type').html('<option value=""><?php echo e(__("Select a location")); ?></option>');
    $('#appointment_location_value').val('');
    $('#appointment_date').val('');
    // Set timezone to GMT by default
    $('#appointment_timezone').val('GMT');
    // Clear global event data
    window.selectedEventData = null;
}
    // Update your editEvent function to populate custom fields if they exist:
    // (This function is removed to avoid duplication - see the main editEvent function below)
// Add this to your global functions at the bottom:
window.toggleCustomField = toggleCustomField;//custom field

// Global variable to store selected locations
let selectedLocations = [];

// Open location modal
function openLocationModal(locationType) {
    const modalId = locationType + 'LocationModal';
    const modal = new bootstrap.Modal(document.getElementById(modalId));
    modal.show();
}
// Save location from modal
function saveLocation(locationType) {
    let locationData = {
        type: locationType,
        value: '',
        display: ''
    };
    // Get the value based on location type
    switch(locationType) {
        case 'zoom':
            locationData.value = $('#zoom_link').val();
            locationData.display = 'Zoom';
            break;
        case 'in_person':
            locationData.value = $('#in_person_address').val();
            locationData.display = 'In-person meeting';
            break;
        case 'phone':
            locationData.value = $('#phone_number').val();
            locationData.display = 'Phone call';
            break;
        case 'meet':
            locationData.value = $('#meet_link').val();
            locationData.display = 'Google Meet';
            break;
        case 'skype':
            locationData.value = $('#skype_link').val();
            locationData.display = 'Skype';
            break;
        case 'others':
            locationData.value = $('#others_details').val();
            locationData.display = 'Others';
            break;
    }

    // Validate that value is provided
    if (!locationData.value.trim()) {
        alert('Please enter the location details');
        return;
    }

    // Check if this location type already exists
    const existingIndex = selectedLocations.findIndex(loc => loc.type === locationType);
    if (existingIndex !== -1) {
        // Update existing location
        selectedLocations[existingIndex] = locationData;
    } else {
        // Add new location
        selectedLocations.push(locationData);
    }

    // Update the display
    updateSelectedLocationsDisplay();

    // Close the modal
    const modalId = locationType + 'LocationModal';
    const modal = bootstrap.Modal.getInstance(document.getElementById(modalId));
    modal.hide();

    // Clear the form
    clearLocationModal(locationType);
}

// Clear location modal form
function clearLocationModal(locationType) {
    switch(locationType) {
        case 'zoom':
            $('#zoom_link').val('');
            break;
        case 'in_person':
            $('#in_person_address').val('');
            break;
        case 'phone':
            $('#phone_number').val('');
            break;
        case 'meet':
            $('#meet_link').val('');
            break;
        case 'skype':
            $('#skype_link').val('');
            break;
        case 'others':
            $('#others_details').val('');
            break;
    }
}

// Remove location
function removeLocation(locationType) {
    selectedLocations = selectedLocations.filter(loc => loc.type !== locationType);
    updateSelectedLocationsDisplay();
}

// Update the display of selected locations
function updateSelectedLocationsDisplay() {
    const container = $('#selected-locations-container');
    container.empty();

    if (selectedLocations.length === 0) {
        container.hide();
        $('#event_locations_data').val('');
        return;
    }

    container.show();

    selectedLocations.forEach(location => {
        const chip = createLocationChip(location);
        container.append(chip);
    });

    // Update hidden input with location data
    $('#event_locations_data').val(JSON.stringify(selectedLocations));
}

// Create location chip element
function createLocationChip(location) {
    const iconMap = {
        'zoom': 'ti-video text-primary',
        'in_person': 'ti-map-pin text-success',
        'phone': 'ti-phone text-info',
        'meet': 'ti-brand-google text-warning',
        'skype': 'ti-brand-skype text-primary',
        'others': 'ti-dots text-secondary'
    };

    const icon = iconMap[location.type] || 'ti-map-pin';

    return $(`
        <div class="d-inline-flex align-items-center bg-light border rounded-pill px-3 py-2 me-2 mb-2">
            <i class="ti ${icon} me-2"></i>
            <span class="fw-medium">${location.display}</span>
            <button type="button" class="btn btn-sm btn-link text-danger p-0 ms-2" onclick="removeLocation('${location.type}')" title="Remove">
                <i class="ti ti-x"></i>
            </button>
        </div>
    `);
}

// Legacy function for backward compatibility
function toggleLocationFields() {
    // This function is kept for backward compatibility but the new system doesn't use it
    console.log('toggleLocationFields called - using new location system');
}

// Reset form to initial state
function resetForm() {
    $('#createEventForm')[0].reset();

    // Clear selected locations
    selectedLocations = [];
    updateSelectedLocationsDisplay();

    // Clear all location modal forms
    clearLocationModal('zoom');
    clearLocationModal('in_person');
    clearLocationModal('phone');
    clearLocationModal('meet');
    clearLocationModal('skype');
    clearLocationModal('others');

    // Clear custom fields container
    $('#custom-fields-container').empty();
    customFieldCounter = 0;

    // Reset minimum notice to default values
    $('#minimum_notice_value').val(0);
    $('#minimum_notice_unit').val('minutes');
    $('#minimum_notice').val(0);
    updateMinimumNoticePreview(0, 'minutes');

    // Reset status to default value
    $('#event_status').val('active');

    // Reset date range fields to default (indefinitely)
    $('#indefinitely_option').prop('checked', true);
    $('#calendar_days_input').val(30);
    $('#date_range_start_input').val('');
    $('#date_range_end_input').val('');
    handleDateRangeChange(); // Update field states

    // Reset date override fields
    $('#override_date').val('');
    $('#override_time').val('');
    $('#unavailable-slots-list').empty();

    // Reset modal title and button text
    $('.modal-title').text('<?php echo e(__("Create New Event")); ?>');
    $('#submitBtn').text('<?php echo e(__("Create Event")); ?>');

    // Reset editing state
    editingEventId = null;

    console.log('Form reset completed');
}

// Load staff data for dropdown
function loadStaffData() {
    $.ajax({
        url: '<?php echo e(route("calendar-events.staff-data")); ?>',
        type: 'GET',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            console.log('Staff data response:', response);
            if (response.success) {
                const staffSelect = $('#assigned_staff_id');
                staffSelect.empty();
                staffSelect.append('<option value=""><?php echo e(__("Select Staff (Optional)")); ?></option>');

                if (response.data && response.data.length > 0) {
                    response.data.forEach(function(staff) {
                        const typeLabel = staff.type.charAt(0).toUpperCase() + staff.type.slice(1);
                        staffSelect.append(`<option value="${staff.id}">${staff.name} (${typeLabel})</option>`);
                    });
                    console.log(`Loaded ${response.data.length} staff members`);
                } else {
                    console.log('No staff data found');
                }
            } else {
                console.error('Failed to load staff data:', response.message);
            }
        },
        error: function(xhr) {
            console.error('Error loading staff data:', xhr);
        }
    });
}

// Open create event modal
function openCreateEventModal() {
    console.log('Opening create modal...');
    resetForm();
    loadStaffData();
    $('#createEventModal').modal('show');
}

// Edit event function
function editEvent(eventId) {
    console.log('Editing event:', eventId);
    
    // Set editing state
    editingEventId = eventId;
    
    // Update modal title and button text
    $('.modal-title').text('<?php echo e(__("Event Details")); ?>');
    $('#submitBtn').text('<?php echo e(__("Update Event")); ?>');
    
    // Load event data
    const url = `<?php echo e(url('calendar-events')); ?>/${eventId}/edit-data`;
    console.log('Making AJAX request to:', url);

    $.ajax({
        url: url,
        method: 'GET',
        success: function(response) {
            console.log('Event data loaded:', response);
            
            if (response.success) {
                const event = response.data;
                
                // Populate form fields
                $('#event_title').val(event.title || '');
                $('#event_duration').val(event.duration || 60);
                $('#booking_per_slot').val(event.booking_per_slot || 1);

                // Set minimum notice using the new format
                setMinimumNoticeFromMinutes(event.minimum_notice || 0);

                $('#event_description').val(event.description || '');
                $('#custom_redirect_url').val(event.custom_redirect_url || '');

                // Set status field
                $('#event_status').val(event.status || 'active');

                // Handle location data - check if new format exists, otherwise use legacy format
                selectedLocations = [];

                if (event.locations_data) {
                    // New format - parse JSON data
                    try {
                        selectedLocations = JSON.parse(event.locations_data);
                    } catch (e) {
                        console.error('Error parsing locations_data:', e);
                        selectedLocations = [];
                    }
                } else if (event.location) {
                    // Legacy format - convert to new format
                    const legacyLocation = {
                        type: event.location,
                        display: getLocationDisplayName(event.location),
                        value: ''
                    };

                    // Set the value based on location type
                    if (event.location === 'in_person') {
                        legacyLocation.value = event.physical_address || '';
                    } else if (['zoom', 'skype', 'meet', 'others'].includes(event.location)) {
                        legacyLocation.value = event.meet_link || '';
                    }

                    if (legacyLocation.value) {
                        selectedLocations = [legacyLocation];
                    }
                }

                // Update the location display
                updateSelectedLocationsDisplay();

                // Populate custom fields if they exist
                populateCustomFieldsForEdit(event);

                // Populate date range fields
                if (event.date_range_type) {
                    $(`input[name="date_range_type"][value="${event.date_range_type}"]`).prop('checked', true);

                    if (event.date_range_type === 'calendar_days' && event.date_range_days) {
                        $('#calendar_days_input').val(event.date_range_days);
                    } else if (event.date_range_type === 'date_range') {
                        if (event.date_range_start) {
                            $('#date_range_start_input').val(event.date_range_start);
                        }
                        if (event.date_range_end) {
                            $('#date_range_end_input').val(event.date_range_end);
                        }
                    }
                } else {
                    // Default to indefinitely for backward compatibility
                    $('#indefinitely_option').prop('checked', true);
                }

                // Trigger date range change to enable/disable appropriate fields
                handleDateRangeChange();

                // Populate date overrides if they exist
                $('#unavailable-slots-list').empty(); // Clear existing overrides
                if (event.date_override) {
                    let overrides = [];
                    if (typeof event.date_override === 'string') {
                        try {
                            overrides = JSON.parse(event.date_override);
                        } catch (e) {
                            console.error('Error parsing date_override:', e);
                        }
                    } else if (Array.isArray(event.date_override)) {
                        overrides = event.date_override;
                    }

                    overrides.forEach(function(override) {
                        if (typeof override === 'string') {
                            populateDateOverride(override);
                        } else if (override.datetime) {
                            populateDateOverride(override.datetime);
                        }
                    });
                }

                // Load staff data and set selected staff
                loadStaffData();
                setTimeout(function() {
                    $('#assigned_staff_id').val(event.assigned_staff_id || '');
                }, 100);

                // Show modal
                $('#createEventModal').modal('show');
            } else {
                show_toastr('error', response.message || 'Failed to load event');
            }
        },
        error: function(xhr) {
            console.log('Error loading event:', xhr.responseText);
            show_toastr('error', 'Error loading event details. Please try again.');
        }
    });
}



// Switch between views
function switchView(viewType) {
    console.log('switchView called with viewType:', viewType);

    try {
        // Remove active classes and set all buttons to inactive state
        $('#calendar-btn, #events-btn, #appointment-btn, #bookings-btn').each(function() {
            console.log('Resetting button:', this.id);
            $(this).removeClass('active btn-primary bg-primary').addClass('btn-outline-primary');
        });

        // Add active class and styling to the clicked button
        let activeBtn;
        if(viewType === 'calendar') {
            activeBtn = $('#calendar-btn');
        } else if(viewType === 'events') {
            activeBtn = $('#events-btn');
        } else if(viewType === 'appointment') {
            activeBtn = $('#appointment-btn');
        } else if(viewType === 'bookings') {
            activeBtn = $('#bookings-btn');
        }

        if(activeBtn && activeBtn.length) {
            console.log('Setting active button:', activeBtn.attr('id'));
            activeBtn.removeClass('btn-outline-primary').addClass('active btn-primary bg-primary');
        } else {
            console.error('Active button not found for viewType:', viewType);
        }
    } catch(error) {
        console.error('Error in switchView:', error);
    }

    // Show/hide sections
    $('#calendar-section, #events-section, #appointment-section, #bookings-section').hide();
    $('#' + viewType + '-section').show();
    // Show/hide create button
    const createBtn = $('#create-event-btn');
    if (viewType === 'events') {
        createBtn.removeClass('d-none');
        loadEventsList(currentPage, currentPerPage);
    } else {
        createBtn.addClass('d-none');
    }
    // Refresh calendar if switching to calendar view
    if (viewType === 'calendar' && calendar) {
        calendar.refetchEvents();
    }
}
// Global variables for pagination
let currentPage = 1;
let currentPerPage = 10;

// Load events list with pagination
function loadEventsList(page = 1, perPage = null) {
    console.log('Loading events list...', { page, perPage });

    // Use provided perPage or current setting
    if (perPage !== null) {
        currentPerPage = perPage;
    }
    currentPage = page;

    $.ajax({
        url: '<?php echo e(route("calendar-events.index")); ?>',
        method: 'GET',
        data: {
            page: currentPage,
            per_page: currentPerPage
        },
        success: function(response) {
            console.log('Events loaded:', response);
            
            const tbody = $('#events-table tbody');
            tbody.empty();
            
            if (response.success && response.data && response.data.length > 0) {
                response.data.forEach(function(event) {
                    // Add null check for event and required properties
                    if (!event || !event.id || !event.start_date || !event.end_date) {
                        console.warn('Skipping invalid event in events table:', event);
                        return;
                    }

                    // Format date range based on date_range_type
                    let dateRange = '-';
                    if (event.date_range_type) {
                        switch (event.date_range_type) {
                            case 'calendar_days':
                                dateRange = `<span class="badge bg-info">${event.date_range_days || 30} calendar days</span>`;
                                break;
                            case 'date_range':
                                if (event.date_range_start && event.date_range_end) {
                                    const startDate = new Date(event.date_range_start).toLocaleDateString();
                                    const endDate = new Date(event.date_range_end).toLocaleDateString();
                                    dateRange = `<span class="badge bg-primary">${startDate} - ${endDate}</span>`;
                                } else {
                                    dateRange = '<span class="badge bg-warning">Date range not set</span>';
                                }
                                break;
                            case 'indefinitely':
                                dateRange = '<span class="badge bg-success">Indefinitely</span>';
                                break;
                            default:
                                // Fallback to old logic for backward compatibility
                                const startDate = new Date(event.start_date).toLocaleDateString();
                                const endDate = new Date(event.end_date).toLocaleDateString();
                                dateRange = startDate === endDate ? startDate : `${startDate} - ${endDate}`;
                        }
                    } else {
                        // Fallback to old logic for backward compatibility
                        const startDate = new Date(event.start_date).toLocaleDateString();
                        const endDate = new Date(event.end_date).toLocaleDateString();
                        dateRange = startDate === endDate ? startDate : `${startDate} - ${endDate}`;
                    }

                    // Format status with badge
                    const statusBadge = event.status === 'active'
                        ? '<span class="badge bg-success">Active</span>'
                        : '<span class="badge bg-secondary">Inactive</span>';

                    // Format location display with multiple location support
                    let locationDisplay = '-';
                    if (event.locations_data) {
                        try {
                            const locations = JSON.parse(event.locations_data);
                            if (locations && locations.length > 0) {
                                if (locations.length === 1) {
                                    locationDisplay = locations[0].display;
                                } else {
                                    locationDisplay = `${locations[0].display} <span class="badge bg-primary location-count-badge ms-1">+${locations.length - 1}</span>`;
                                }
                            }
                        } catch (e) {
                            console.error('Error parsing locations_data:', e);
                            locationDisplay = event.location || '-';
                        }
                    } else if (event.location) {
                        locationDisplay = event.location;
                    }

                    // Format assigned staff display
                    let assignedStaffDisplay = '-';
                    if (event.assigned_staff) {
                        const staffType = event.assigned_staff.type.charAt(0).toUpperCase() + event.assigned_staff.type.slice(1);
                        assignedStaffDisplay = `
                            <span class="badge bg-primary">
                                <i class="ti ti-user me-1"></i>${event.assigned_staff.name}
                            </span>
                            <small class="d-block text-muted">${staffType}</small>
                        `;
                    } else {
                        assignedStaffDisplay = `
                            <span class="badge bg-light text-muted">
                                <i class="ti ti-user-off me-1"></i>Not Assigned
                            </span>
                        `;
                    }

                    const row = `
                        <tr data-event-id="${event.id}">
                            <td>
                                <div class="form-check">
                                    <input class="form-check-input event-checkbox" type="checkbox" value="${event.id}" id="event-${event.id}">
                                    <label class="form-check-label" for="event-${event.id}"></label>
                                </div>
                            </td>
                            <td><strong>${event.title}</strong></td>
                            <td>${assignedStaffDisplay}</td>
                            <td>${dateRange}</td>
                            <td>${event.duration || 60} min</td>
                            <td>${locationDisplay}</td>
                            <td>${statusBadge}</td>
                            <td>
                                <div class="d-flex flex-column">
                                    <span class="badge bg-success">${event.booking_per_slot || 1} slots</span>
                                    ${event.booking_count > 0 ? `<small class="text-muted mt-1">${event.booking_count} booked</small>` : ''}
                                </div>
                            </td>
                            <td>
                                <!-- View Button -->
                                <div style="position: relative; display: inline-block; margin-right: 6px;">
                                    <button onclick="viewEvent(${event.id})"
                                        style="background: linear-gradient(to right, #0d6efd, #6610f2); border: none; padding: 12px 12px; border-radius: 8px; display: inline-flex; align-items: center; justify-content: center; color: white; transition: transform 0.2s ease, box-shadow 0.2s ease;"
                                        onmouseover="this.style.transform='scale(1.1)'; this.style.boxShadow='0 4px 12px rgba(0,0,0,0.15)'; this.nextElementSibling.style.visibility='visible';"
                                        onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='none'; this.nextElementSibling.style.visibility='hidden';">
                                        <i class="ti ti-eye"></i>
                                    </button>
                                    <span style="visibility: hidden; background-color: black; color: #fff; text-align: center; padding: 4px 8px; border-radius: 4px; position: absolute; top: -30px; left: 50%; transform: translateX(-50%); white-space: nowrap; font-size: 12px; pointer-events: none;">
                                        View
                                    </span>
                                </div>

                                <!-- Edit Button -->
                                <div style="position: relative; display: inline-block; margin-right: 6px;">
                                    <button onclick="editEvent(${event.id})"
                                        style="background: linear-gradient(to right, #0f5132, #198754); border: none; padding: 12px 12px; border-radius: 8px; display: inline-flex; align-items: center; justify-content: center; color: white; transition: transform 0.2s ease, box-shadow 0.2s ease;"
                                        onmouseover="this.style.transform='scale(1.1)'; this.style.boxShadow='0 4px 12px rgba(0,0,0,0.15)'; this.nextElementSibling.style.visibility='visible';"
                                        onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='none'; this.nextElementSibling.style.visibility='hidden';">
                                        <i class="ti ti-pencil"></i>
                                    </button>
                                    <span style="visibility: hidden; background-color: black; color: #fff; text-align: center; padding: 4px 8px; border-radius: 4px; position: absolute; top: -30px; left: 50%; transform: translateX(-50%); white-space: nowrap; font-size: 12px;">
                                        Edit
                                    </span>
                                </div>

                                <!-- Copy Link Button -->
                                <div style="position: relative; display: inline-block; margin-right: 6px;">
                                    <button onclick="copyEventLinkFromList(${event.id}, this)"
                                        style="background: linear-gradient(to right, #6f42c1, #e83e8c); border: none; padding: 12px 12px; border-radius: 8px; display: inline-flex; align-items: center; justify-content: center; color: white; transition: transform 0.2s ease, box-shadow 0.2s ease;"
                                        onmouseover="this.style.transform='scale(1.1)'; this.style.boxShadow='0 4px 12px rgba(0,0,0,0.15)'; this.nextElementSibling.style.visibility='visible';"
                                        onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='none'; this.nextElementSibling.style.visibility='hidden';">
                                        <i class="ti ti-copy"></i>
                                    </button>
                                    <span style="visibility: hidden; background-color: black; color: #fff; text-align: center; padding: 4px 8px; border-radius: 4px; position: absolute; top: -30px; left: 50%; transform: translateX(-50%); white-space: nowrap; font-size: 12px;">
                                        Copy Link
                                    </span>
                                </div>

                                <!-- Delete Button -->
                                <div style="position: relative; display: inline-block;">
                                    <button onclick="deleteEvent(${event.id})"
                                        style="background: white; border: 1px solid #dc3545; padding: 10px 12px; border-radius: 8px; display: inline-flex; align-items: center; justify-content: center; color: #dc3545; transition: transform 0.2s ease, box-shadow 0.2s ease;"
                                        onmouseover="this.style.transform='scale(1.1)'; this.style.boxShadow='0 4px 12px rgba(0,0,0,0.15)'; this.nextElementSibling.style.visibility='visible';"
                                        onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='none'; this.nextElementSibling.style.visibility='hidden';">
                                        <i class="ti ti-trash"></i>
                                    </button>
                                    <span style="visibility: hidden; background-color: black; color: #fff; text-align: center; padding: 4px 8px; border-radius: 4px; position: absolute; top: -30px; left: 50%; transform: translateX(-50%); white-space: nowrap; font-size: 12px;">
                                        Delete
                                    </span>
                                </div>
                            </td>
                        </tr>
                    `;
                    tbody.append(row);
                });
            } else {
                // Update all counters to 0
                $('#events-count').text('0 <?php echo e(__("Events")); ?>');
                $('#active-events-count').text('0 <?php echo e(__("Active")); ?>');
                $('#total-bookings-count').text('0 <?php echo e(__("Bookings")); ?>');
                tbody.html('<tr><td colspan="9" class="text-center">No events found</td></tr>');
            }

            // Update pagination controls
            updatePaginationControls(response.pagination);
        },
        error: function(xhr) {
            console.log('Error loading events:', xhr.responseText);
            // Update all counters to 0 on error
            $('#events-count').text('0 <?php echo e(__("Events")); ?>');
            $('#active-events-count').text('0 <?php echo e(__("Active")); ?>');
            $('#total-bookings-count').text('0 <?php echo e(__("Bookings")); ?>');
            $('#events-table tbody').html('<tr><td colspan="9" class="text-center text-danger">Error loading events</td></tr>');
        }
    });
}

// Update pagination controls
function updatePaginationControls(pagination) {
    if (!pagination) {
        $('#events-pagination').hide();
        return;
    }

    // Update pagination info
    const infoText = `<?php echo e(__('Showing')); ?> ${pagination.from || 0} <?php echo e(__('to')); ?> ${pagination.to || 0} <?php echo e(__('of')); ?> ${pagination.total} <?php echo e(__('entries')); ?>`;
    $('#pagination-info-text').text(infoText);

    // Generate pagination buttons
    const paginationControls = $('#pagination-controls');
    paginationControls.empty();

    // Previous button
    if (pagination.current_page > 1) {
        paginationControls.append(`
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadEventsList(${pagination.current_page - 1}); return false;">
                    <i class="ti ti-chevron-left"></i>
                </a>
            </li>
        `);
    } else {
        paginationControls.append(`
            <li class="page-item disabled">
                <span class="page-link"><i class="ti ti-chevron-left"></i></span>
            </li>
        `);
    }

    // Page numbers
    const startPage = Math.max(1, pagination.current_page - 2);
    const endPage = Math.min(pagination.last_page, pagination.current_page + 2);

    // First page if not in range
    if (startPage > 1) {
        paginationControls.append(`
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadEventsList(1); return false;">1</a>
            </li>
        `);
        if (startPage > 2) {
            paginationControls.append(`<li class="page-item disabled"><span class="page-link">...</span></li>`);
        }
    }

    // Page range
    for (let i = startPage; i <= endPage; i++) {
        if (i === pagination.current_page) {
            paginationControls.append(`
                <li class="page-item active">
                    <span class="page-link">${i}</span>
                </li>
            `);
        } else {
            paginationControls.append(`
                <li class="page-item">
                    <a class="page-link" href="#" onclick="loadEventsList(${i}); return false;">${i}</a>
                </li>
            `);
        }
    }

    // Last page if not in range
    if (endPage < pagination.last_page) {
        if (endPage < pagination.last_page - 1) {
            paginationControls.append(`<li class="page-item disabled"><span class="page-link">...</span></li>`);
        }
        paginationControls.append(`
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadEventsList(${pagination.last_page}); return false;">${pagination.last_page}</a>
            </li>
        `);
    }

    // Next button
    if (pagination.current_page < pagination.last_page) {
        paginationControls.append(`
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadEventsList(${pagination.current_page + 1}); return false;">
                    <i class="ti ti-chevron-right"></i>
                </a>
            </li>
        `);
    } else {
        paginationControls.append(`
            <li class="page-item disabled">
                <span class="page-link"><i class="ti ti-chevron-right"></i></span>
            </li>
        `);
    }

    // Show pagination controls if there's more than one page
    if (pagination.last_page > 1) {
        $('#events-pagination').show();
    } else {
        $('#events-pagination').hide();
    }
}

// Bulk operations helper functions
function getSelectedEventIds() {
    const selectedIds = [];
    $('.event-checkbox:checked').each(function() {
        selectedIds.push(parseInt($(this).val()));
    });
    return selectedIds;
}

function updateSelectAllState() {
    const totalCheckboxes = $('.event-checkbox').length;
    const checkedCheckboxes = $('.event-checkbox:checked').length;

    if (checkedCheckboxes === 0) {
        $('#select-all-events').prop('indeterminate', false).prop('checked', false);
    } else if (checkedCheckboxes === totalCheckboxes) {
        $('#select-all-events').prop('indeterminate', false).prop('checked', true);
    } else {
        $('#select-all-events').prop('indeterminate', true).prop('checked', false);
    }
}

function updateBulkActionsVisibility() {
    const selectedCount = $('.event-checkbox:checked').length;
    const bulkActions = $('#bulk-actions');
    const selectedCountSpan = $('#selected-count');

    if (selectedCount > 0) {
        selectedCountSpan.text(`${selectedCount} selected`);
        bulkActions.show();
    } else {
        bulkActions.hide();
        // Reset bulk status dropdown
        $('#bulk-status').val('');
    }
}

function bulkUpdateStatus(eventIds, status) {
    if (!eventIds || eventIds.length === 0) {
        show_toastr('error', 'No events selected');
        return;
    }

    $.ajax({
        url: '<?php echo e(route("calendar-events.bulk-update-status")); ?>',
        method: 'POST',
        data: {
            event_ids: eventIds,
            status: status,
            _token: '<?php echo e(csrf_token()); ?>'
        },
        success: function(response) {
            if (response.success) {
                show_toastr('success', response.message);
                // Reset selections and reload events
                $('.event-checkbox').prop('checked', false);
                $('#select-all-events').prop('checked', false);
                updateBulkActionsVisibility();
                loadEventsList(currentPage, currentPerPage);
                loadAllEvents(); // Refresh sidebar events
                if (calendar) {
                    calendar.refetchEvents();
                }
            } else {
                show_toastr('error', response.message || 'Failed to update events');
            }
        },
        error: function(xhr) {
            console.error('Error updating events:', xhr);
            show_toastr('error', 'Failed to update events');
        }
    });
}

function bulkDeleteEvents(eventIds) {
    if (!eventIds || eventIds.length === 0) {
        show_toastr('error', 'No events selected');
        return;
    }

    // Show confirmation dialog
    if (!confirm(`Are you sure you want to delete ${eventIds.length} selected event(s)? This action cannot be undone.`)) {
        return;
    }

    $.ajax({
        url: '<?php echo e(route("calendar-events.bulk-delete")); ?>',
        method: 'POST',
        data: {
            event_ids: eventIds,
            _token: '<?php echo e(csrf_token()); ?>'
        },
        success: function(response) {
            if (response.success) {
                show_toastr('success', response.message);
                // Reset selections and reload events
                $('.event-checkbox').prop('checked', false);
                $('#select-all-events').prop('checked', false);
                updateBulkActionsVisibility();
                loadEventsList(currentPage, currentPerPage);
                loadAllEvents(); // Refresh sidebar events
                if (calendar) {
                    calendar.refetchEvents();
                }
            } else {
                show_toastr('error', response.message || 'Failed to delete events');
            }
        },
        error: function(xhr) {
            console.error('Error deleting events:', xhr);
            show_toastr('error', 'Failed to delete events');
        }
    });
}

// View bookings for a specific event
function viewBookings(eventId) {
    $.ajax({
        url: '<?php echo e(route("appointments.index")); ?>',
        method: 'GET',
        data: {
            event_id: eventId
        },
        success: function(response) {
            if (response.success && response.data) {
                let bookingsHtml = '<div class="table-responsive"><table class="table table-sm">';
                bookingsHtml += '<thead><tr><th>Contact Name</th><th>Date</th><th>Time</th><th>Timezone</th></tr></thead><tbody>';

                if (response.data.length > 0) {
                    response.data.forEach(function(booking) {
                        const timezone = booking.custom_fields && booking.custom_fields.timezone ? booking.custom_fields.timezone : 'Not specified';
                        bookingsHtml += `
                            <tr>
                                <td>${booking.name}</td>
                                <td>${new Date(booking.date).toLocaleDateString()}</td>
                                <td>${booking.time}</td>
                                <td><small>${timezone}</small></td>
                            </tr>
                        `;
                    });
                } else {
                    bookingsHtml += '<tr><td colspan="4" class="text-center">No bookings found</td></tr>';
                }

                bookingsHtml += '</tbody></table></div>';

                // Show in a modal or alert
                const modal = `
                    <div class="modal fade" id="bookingsModal" tabindex="-1">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">Event Bookings</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    ${bookingsHtml}
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // Remove existing modal if any
                $('#bookingsModal').remove();

                // Add modal to body and show
                $('body').append(modal);
                $('#bookingsModal').modal('show');
            }
        },
        error: function(xhr) {
            alert('Error loading bookings: ' + xhr.responseText);
        }
    });
}

function collectDateOverrideData() {
    const overrides = [];
    document.querySelectorAll('input[name="date_override[]"]').forEach(input => {
        if (input.value) {
            overrides.push(input.value);
        }
    });
    return overrides;
}





// View event details in modal
function viewEvent(eventId) {
    console.log('Loading event details for ID:', eventId);

    // Validate eventId
    if (!eventId) {
        console.error('No event ID provided');
        showEventError('Invalid event ID');
        return;
    }

    // Show loading state
    $('#view-event-title').text('Loading...');
    $('#view-event-duration').text('');
    $('#view-event-booking-slots').text('');
    $('#view-event-notice').text('');
    $('#view-description-row').hide();
    $('#view-location-section').hide();
    $('#viewEventModal').modal('show');

    // First, try to get the event from the calendar events index
    $.ajax({
        url: '<?php echo e(route("calendar-events.index")); ?>',
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        timeout: 10000, // 10 second timeout
        success: function(response) {
            console.log('Events response:', response);

            if (response.success && response.data && Array.isArray(response.data)) {
                // Find the specific event by ID
                const event = response.data.find(e => e.id == eventId);

                if (event) {
                    console.log('Found event:', event);
                    populateEventModal(event, eventId);
                } else {
                    console.error('Event not found in response. Available events:', response.data.map(e => e.id));
                    showEventError('Event not found. The event may have been deleted or you may not have permission to view it.');
                }
            } else if (response.success === false) {
                console.error('API returned error:', response.message);
                showEventError(response.message || 'Failed to load event details');
            } else {
                console.error('Invalid response format:', response);
                showEventError('Invalid response from server');
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX error:', {xhr, status, error});

            let errorMessage = 'Error loading event details';

            if (xhr.status === 403) {
                errorMessage = 'You do not have permission to view this event';
            } else if (xhr.status === 404) {
                errorMessage = 'Event not found';
            } else if (xhr.status === 500) {
                errorMessage = 'Server error occurred';
            } else if (status === 'timeout') {
                errorMessage = 'Request timed out. Please try again.';
            } else if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }

            showEventError(errorMessage);
        }
    });
}

// Helper function to populate the event modal
function populateEventModal(event, eventId) {
    try {
        // Validate event object
        if (!event || typeof event !== 'object') {
            console.error('Invalid event object:', event);
            showEventError('Invalid event data');
            return;
        }

        // Check if modal elements exist
        const requiredElements = [
            '#view-event-title',
            '#view-event-duration', '#view-event-booking-slots', '#view-event-notice'
        ];

        for (const elementId of requiredElements) {
            if ($(elementId).length === 0) {
                console.error('Required modal element not found:', elementId);
                showEventError('Modal elements not found. Please refresh the page.');
                return;
            }
        }

        // Set the event ID for copying the link
        $('#viewEventModal').data('event-id', eventId);

        // Populate basic event details
        $('#view-event-title').text(event.title || 'Untitled Event');



        // Event settings with safe defaults
        const duration = event.duration || 60;
        const bookingSlots = event.booking_per_slot || 1;
        const minimumNotice = event.minimum_notice || 0;

        $('#view-event-duration').text(duration + ' minutes');
        $('#view-event-booking-slots').text(bookingSlots);
        $('#view-event-notice').text(getNoticeText(minimumNotice));

        // Description
        if (event.description && typeof event.description === 'string' && event.description.trim()) {
            $('#view-event-description').text(event.description);
            $('#view-description-row').show();
        } else {
            $('#view-description-row').hide();
        }

        // Location details - handle new multi-location system
        let hasLocations = false;

        if (event.locations_data) {
            // New multi-location system
            try {
                const locations = JSON.parse(event.locations_data);
                if (locations && locations.length > 0) {
                    hasLocations = true;

                    // Display all locations
                    const locationDisplay = locations.map(loc => loc.display).join(', ');
                    $('#view-event-location').text(locationDisplay);
                    $('#view-location-section').show();

                    // Show details for each location type
                    let hasAddress = false;
                    let hasLink = false;
                    let addressText = '';
                    let linkText = '';
                    let linkHref = '#';

                    locations.forEach(location => {
                        if (location.type === 'in_person' && location.value) {
                            hasAddress = true;
                            addressText += (addressText ? '\n' : '') + location.value;
                        } else if (['zoom', 'meet', 'skype', 'phone', 'others'].includes(location.type) && location.value) {
                            hasLink = true;
                            linkText += (linkText ? ', ' : '') + location.value;
                            // Format phone numbers as tel: links
                            if (linkHref === '#') {
                                if (location.type === 'phone' && /^[\+]?[0-9\s\-\(\)]+$/.test(location.value.trim())) {
                                    linkHref = 'tel:' + location.value.trim();
                                } else {
                                    linkHref = location.value;
                                }
                            }
                        }
                    });

                    // Show address if any in-person locations
                    if (hasAddress) {
                        $('#view-event-address').text(addressText);
                        $('#view-address-row').show();
                    } else {
                        $('#view-address-row').hide();
                    }

                    // Show all online locations
                    const onlineLocations = locations.filter(loc => ['zoom', 'meet', 'skype', 'phone', 'others'].includes(loc.type));
                    if (onlineLocations.length > 0) {
                        const locationIcons = {
                            'zoom': 'ti ti-video',
                            'meet': 'ti ti-brand-google',
                            'skype': 'ti ti-brand-skype',
                            'phone': 'ti ti-phone',
                            'others': 'ti ti-dots'
                        };

                        let meetingLocationsHtml = '';
                        onlineLocations.forEach((location, index) => {
                            const icon = locationIcons[location.type] || 'ti ti-link';
                            const href = location.type === 'phone' && /^[\+]?[0-9\s\-\(\)]+$/.test(location.value.trim())
                                ? 'tel:' + location.value.trim()
                                : location.value;

                            const chipClass = `modern-location-chip ${location.type}-chip`;

                            meetingLocationsHtml += `
                                <a href="${href}" target="_blank" class="${chipClass}" onclick="handleMeetingLinkClick(event)">
                                    <i class="location-icon ${icon}"></i>
                                    <span class="location-name">${location.display || location.type}</span>
                                </a>
                            `;
                        });

                        $('#view-meeting-locations-container').html(meetingLocationsHtml);
                        $('#view-link-row').show();
                    } else {
                        $('#view-link-row').hide();
                    }

                    // Set dynamic column classes based on what's visible
                    updateLocationColumnLayout();
                }
            } catch (e) {
                console.error('Error parsing locations_data:', e);
                $('#view-event-location').text('Location data error');
                $('#view-location-section').show();
                $('#view-address-row').hide();
                $('#view-link-row').hide();
                hasLocations = true;
            }
        } else if (event.location && typeof event.location === 'string') {
            // Legacy single location system
            hasLocations = true;
            const locationLabels = {
                'in_person': 'In Person',
                'zoom': 'Zoom',
                'skype': 'Skype',
                'meet': 'Google Meet',
                'phone': 'Phone Call',
                'others': 'Others'
            };

            $('#view-event-location').text(locationLabels[event.location] || event.location);
            $('#view-location-section').show();

            // Handle physical address or meeting link
            if (event.location === 'in_person') {
                if (event.physical_address && typeof event.physical_address === 'string' && event.physical_address.trim()) {
                    $('#view-event-address').text(event.physical_address);
                    $('#view-address-row').show();
                } else {
                    $('#view-event-address').text('Address not specified');
                    $('#view-address-row').show();
                }
                $('#view-link-row').hide();
            } else {
                // For online meetings and phone calls
                const locationIcons = {
                    'zoom': 'ti ti-video',
                    'meet': 'ti ti-brand-google',
                    'skype': 'ti ti-brand-skype',
                    'phone': 'ti ti-phone',
                    'others': 'ti ti-dots'
                };

                let meetingLocationsHtml = '';
                const icon = locationIcons[event.location] || 'ti ti-link';
                const locationName = locationLabels[event.location] || event.location;

                const chipClass = `modern-location-chip ${event.location}-chip`;

                if (event.meet_link && typeof event.meet_link === 'string' && event.meet_link.trim() && event.meet_link !== '#') {
                    const meetLink = event.meet_link.trim();
                    const href = /^[\+]?[0-9\s\-\(\)]+$/.test(meetLink)
                        ? 'tel:' + meetLink
                        : meetLink;

                    meetingLocationsHtml = `
                        <a href="${href}" target="_blank" class="${chipClass}" onclick="handleMeetingLinkClick(event)">
                            <i class="location-icon ${icon}"></i>
                            <span class="location-name">${locationName}</span>
                        </a>
                    `;
                } else {
                    meetingLocationsHtml = `
                        <div class="${chipClass}" style="cursor: default; opacity: 0.7;">
                            <i class="location-icon ${icon}"></i>
                            <span class="location-name">${locationName}</span>
                            <small class="ms-2 text-muted">(No link)</small>
                        </div>
                    `;
                }

                $('#view-meeting-locations-container').html(meetingLocationsHtml);
                $('#view-link-row').show();
                $('#view-address-row').hide();

                // Set dynamic column classes
                updateLocationColumnLayout();
            }
        }

        if (!hasLocations) {
            $('#view-location-section').hide();
        }

        console.log('Event modal populated successfully');

    } catch (error) {
        console.error('Error populating event modal:', error);
        showEventError('Error displaying event details: ' + error.message);
    }
}

// Helper function to update location column layout dynamically
function updateLocationColumnLayout() {
    const hasAddress = $('#view-address-row').is(':visible');
    const hasOnlineLocations = $('#view-link-row').is(':visible');

    // Remove existing column classes
    $('#view-address-row, #view-link-row').removeClass('col-12 col-md-6 col-lg-4');

    if (hasAddress && hasOnlineLocations) {
        // Both types present - use two columns
        $('#view-address-row, #view-link-row').addClass('col-md-6');
    } else if (hasAddress || hasOnlineLocations) {
        // Only one type present - use full width
        $('#view-address-row, #view-link-row').addClass('col-12');
    }
}

// Helper function to show error in modal
function showEventError(message) {
    $('#view-event-title').text('Error');
    $('#view-event-duration').text('N/A');
    $('#view-event-booking-slots').text('N/A');
    $('#view-event-notice').text('N/A');
    $('#view-description-row').hide();
    $('#view-location-section').hide();
}

// Helper function to format notice text
function getNoticeText(minutes) {
    // Handle null, undefined, or invalid values
    if (!minutes || isNaN(minutes) || minutes <= 0) {
        return 'No notice required';
    }

    // Convert to number if it's a string
    const numMinutes = parseInt(minutes);

    if (isNaN(numMinutes) || numMinutes <= 0) {
        return 'No notice required';
    }

    const hours = Math.floor(numMinutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
        const remainingHours = hours % 24;
        if (remainingHours > 0) {
            return `${days} day${days > 1 ? 's' : ''} ${remainingHours} hour${remainingHours > 1 ? 's' : ''}`;
        }
        return `${days} day${days > 1 ? 's' : ''}`;
    }

    if (hours > 0) {
        const remainingMinutes = numMinutes % 60;
        if (remainingMinutes > 0) {
            return `${hours} hour${hours > 1 ? 's' : ''} ${remainingMinutes} minute${remainingMinutes > 1 ? 's' : ''}`;
        }
        return `${hours} hour${hours > 1 ? 's' : ''}`;
    }

    return `${numMinutes} minute${numMinutes > 1 ? 's' : ''}`;
}

// Delete event
function deleteEvent(eventId) {
    if (confirm('Are you sure you want to delete this event?')) {
        $.ajax({
            url: `<?php echo e(url('calendar-events')); ?>/${eventId}`,
            method: 'DELETE',
            data: {
                "_token": "<?php echo e(csrf_token()); ?>"
            },
            success: function(response) {
                if (response.success) {
                    show_toastr('success', 'Event deleted successfully!');
                    loadEventsList(currentPage, currentPerPage);
                    loadAllEvents(); // Refresh sidebar events
                    if (calendar) {
                        calendar.refetchEvents();
                    }
                } else {
                    show_toastr('error', response.message || 'Failed to delete event');
                }
            },
            error: function(xhr) {
                console.log('Delete error:', xhr.responseText);
                show_toastr('error', 'Error deleting event. Please try again.');
            }
        });
    }
}

// Make functions global
window.openCreateEventModal = openCreateEventModal;
window.switchView = switchView;
window.deleteEvent = deleteEvent;
window.viewEvent = viewEvent;
window.editEvent = editEvent;
window.toggleLocationFields = toggleLocationFields;
window.collectDateOverrideData = collectDateOverrideData;
window.showCopyEventToast = showCopyEventToast;
window.hideCopyEventToast = hideCopyEventToast;
window.filterAppointments = filterAppointments;
window.applyAppointmentFilter = applyAppointmentFilter;

// Test function for copy event link
window.testCopyEventLink = function() {
    console.log('Testing copy event link functionality...');
    console.log('copyEventLinkFromList function exists:', typeof window.copyEventLinkFromList);

    // Test the copy function with a dummy event ID
    if (typeof window.copyEventLinkFromList === 'function') {
        console.log('Testing copyEventLinkFromList with dummy data...');
        // Create a temporary test button
        const testBtn = document.createElement('button');
        testBtn.innerHTML = '<i class="ti ti-copy"></i>';
        window.copyEventLinkFromList(123, testBtn);
    }
};

//weekly available
// Initialize with one slot per day when enabled
$('.day-availability input[type="checkbox"]').change(function() {
    const day = $(this).closest('.day-availability').find('.day-slots');
    if (this.checked) {
        day.show();
        if (day.find('.time-slot').length === 0) {
            // Get the day name from the checkbox id (e.g., 'monday-checkbox' -> 'monday')
            const dayName = $(this).attr('id').replace('-checkbox', '');
            addTimeSlot(dayName);
        }
    } else {
        day.hide();
    }
});

// Add slot functionality - removed duplicate handler to prevent multiple slots being added

// Remove slot functionality - allow removal of any slot without restrictions
$(document).on('click', '.remove-slot-btn', function() {
    $(this).closest('.time-slot').remove();
});

// Removed duplicate addNewSlot function - using addTimeSlot instead

//date override
let unavailableSlots = [];

// Function to toggle the visibility of the date override field
function toggleDateOverride() {
    const overrideField = document.getElementById('date-override-field');
    if (overrideField.style.display === 'none') {
        overrideField.style.display = 'block';
    } else {
        overrideField.style.display = 'none';
    }
}

// Function to add the selected date and time as an unavailable slot
function addUnavailableSlot() {
    const dateInput = document.getElementById('override_date');
    const timeSelect = document.getElementById('override_time');
    const container = document.getElementById('unavailable-slots-list');

    const dateValue = dateInput.value;
    const timeValue = timeSelect.value;

    if (!dateValue) {
        alert("Please select a date.");
        return;
    }

    if (!timeValue) {
        alert("Please select a time.");
        return;
    }

    // Combine date and time into datetime-local format
    const datetimeValue = `${dateValue}T${timeValue}`;

    // Check for duplicates
    const exists = [...document.querySelectorAll('input[name="date_override[]"]')]
        .some(i => i.value === datetimeValue);
    if (exists) {
        alert("This date and time slot is already added.");
        return;
    }

    const wrapper = document.createElement('div');
    wrapper.className = 'd-flex align-items-center mb-2 gap-2';

    // Format display text
    const dateObj = new Date(datetimeValue);
    const readable = dateObj.toLocaleString('en-US', {
        weekday: 'short',
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });

    wrapper.innerHTML = `
        <input type="hidden" name="date_override[]" value="${datetimeValue}">
        <span class="badge bg-danger flex-grow-1 py-2 px-3">
            <i class="ti ti-calendar-x me-1"></i>
            ${readable}
        </span>
        <button type="button" class="btn btn-sm btn-outline-danger" onclick="this.parentElement.remove()" title="Remove override">
            <i class="ti ti-trash"></i>
        </button>
    `;

    container.appendChild(wrapper);

    // Reset inputs
    dateInput.value = '';
    timeSelect.value = '';
}

// Function to populate date override when editing an event
function populateDateOverride(datetimeValue) {
    const container = document.getElementById('unavailable-slots-list');

    const wrapper = document.createElement('div');
    wrapper.className = 'd-flex align-items-center mb-2 gap-2';

    // Format display text
    const dateObj = new Date(datetimeValue);
    const readable = dateObj.toLocaleString('en-US', {
        weekday: 'short',
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });

    wrapper.innerHTML = `
        <input type="hidden" name="date_override[]" value="${datetimeValue}">
        <span class="badge bg-danger flex-grow-1 py-2 px-3">
            <i class="ti ti-calendar-x me-1"></i>
            ${readable}
        </span>
        <button type="button" class="btn btn-sm btn-outline-danger" onclick="this.parentElement.remove()" title="Remove override">
            <i class="ti ti-trash"></i>
        </button>
    `;

    container.appendChild(wrapper);
}


// Function to update the unavailable slots list
function updateUnavailableSlotsList() {
    const slotsList = document.getElementById('unavailable-slots-list');
    slotsList.innerHTML = ''; // Clear the list

    unavailableSlots.forEach((slot, index) => {
        const listItem = document.createElement('li');
        listItem.textContent = slot.toLocaleString(); // Format the date for display

        // Create a delete button for each slot
        const deleteButton = document.createElement('button');
        deleteButton.textContent = 'Delete';
        deleteButton.className = 'btn btn-danger btn-sm ms-2';
        deleteButton.onclick = () => deleteUnavailableSlot(index); // Bind delete function

        listItem.appendChild(deleteButton);
        slotsList.appendChild(listItem);
    });
}

// Function to delete an unavailable slot
function deleteUnavailableSlot(index) {
    unavailableSlots.splice(index, 1); // Remove the slot from the array
    updateUnavailableSlotsList(); // Update the displayed list
    alert('The selected time slot has been removed.');
}

// Function to check if a selected time slot is available for booking
function isTimeSlotAvailable(selectedDate) {
    return !unavailableSlots.some(slot => slot.getTime() === selectedDate.getTime());
}

// Example function to create an appointment
function createAppointment(selectedDate) {
    if (!isTimeSlotAvailable(selectedDate)) {
        alert('This time slot is unavailable. Please choose a different time.');
        return;
    }

    // Proceed with appointment creation logic
    // For example, you can send the appointment data to the server here
    alert(`Appointment created for ${selectedDate.toLocaleString()}`);
}

// Show custom toast for copy event link
function showCopyEventToast(message = 'Event link copied!') {
    console.log('showCopyEventToast called with message:', message);

    const toast = document.getElementById('copy-event-toast');
    const msg = document.getElementById('copy-event-toast-message');

    if (toast && msg) {
        msg.textContent = message;
        toast.style.display = 'block';
        toast.setAttribute('aria-hidden', 'false');
        // Auto-hide after 2.5s
        clearTimeout(window._copyEventToastTimeout);
        window._copyEventToastTimeout = setTimeout(hideCopyEventToast, 2500);
        console.log('Toast displayed successfully');
    } else {
        // Fallback to alert if toast elements not found
        console.log('Toast elements not found, using alert fallback');
        alert(message);
    }
}
function hideCopyEventToast() {
    const toast = document.getElementById('copy-event-toast');
    if (toast) {
        toast.style.display = 'none';
        toast.setAttribute('aria-hidden', 'true');
    }
}
// Copy event link function for Events List (with direct event ID)
window.copyEventLinkFromList = function(eventId, btn) {
    console.log('copyEventLinkFromList called with eventId:', eventId, 'button:', btn);

    if (!eventId) {
        console.error('No event ID provided');
        showCopyEventToast('No event ID provided');
        updateCopyButtonList(btn, false);
        return;
    }

    // Copy the event page link
    const linkToCopy = `${window.location.origin}/calendar-events/${eventId}`;
    const successMessage = 'Event link copied!';

    console.log('Copying link:', linkToCopy);

    // Check if clipboard API is available and we're in a secure context
    const isSecureContext = window.isSecureContext || location.protocol === 'https:' || location.hostname === 'localhost';
    console.log('Secure context:', isSecureContext);
    console.log('Clipboard API available:', !!navigator.clipboard);

    if (!navigator.clipboard || !isSecureContext) {
        console.log('Using fallback copy method');
        // Fallback for older browsers or non-secure contexts
        fallbackCopyTextToClipboardList(linkToCopy, btn, successMessage);
        return;
    }

    // Use modern clipboard API
    console.log('Using modern clipboard API');
    navigator.clipboard.writeText(linkToCopy).then(function() {
        console.log('Link copied successfully');
        showCopyEventToast(successMessage);
        updateCopyButtonList(btn, true);
    }).catch(function(err) {
        console.error('Failed to copy link with clipboard API:', err);
        console.log('Falling back to legacy copy method');
        // Try fallback method if clipboard API fails
        fallbackCopyTextToClipboardList(linkToCopy, btn, successMessage);
    });
}

// Enhanced copyEventLink function with better error handling (for modal - kept for backward compatibility)
window.copyEventLink = function(btn) {
    console.log('copyEventLink called with button:', btn);
    console.log('Button element:', $(btn));
    console.log('Modal element:', $('#viewEventModal'));

    // Get current event ID from the modal
    const eventId = $('#viewEventModal').data('event-id');
    console.log('Event ID from modal:', eventId);
    console.log('Modal data attributes:', $('#viewEventModal').data());

    if (!eventId) {
        console.error('No event ID found');
        showCopyEventToast('No event selected to copy link');
        updateCopyButton(btn, false);
        return;
    }

    // Copy the event page link
    const linkToCopy = `${window.location.origin}/calendar-events/${eventId}`;
    const successMessage = 'Event link copied!';

    console.log('Copying link:', linkToCopy);

    // Check if clipboard API is available and we're in a secure context
    const isSecureContext = window.isSecureContext || location.protocol === 'https:' || location.hostname === 'localhost';
    console.log('Secure context:', isSecureContext);
    console.log('Clipboard API available:', !!navigator.clipboard);

    if (!navigator.clipboard || !isSecureContext) {
        console.log('Using fallback copy method');
        // Fallback for older browsers or non-secure contexts
        fallbackCopyTextToClipboard(linkToCopy, btn, successMessage);
        return;
    }

    // Use modern clipboard API
    console.log('Using modern clipboard API');
    navigator.clipboard.writeText(linkToCopy).then(function() {
        console.log('Link copied successfully');
        showCopyEventToast(successMessage);
        updateCopyButton(btn, true);
    }).catch(function(err) {
        console.error('Failed to copy link with clipboard API:', err);
        console.log('Falling back to legacy copy method');
        // Try fallback method if clipboard API fails
        fallbackCopyTextToClipboard(linkToCopy, btn, successMessage);
    });
}

// Fallback function for older browsers
function fallbackCopyTextToClipboard(text, btn, successMessage) {
    console.log('Using fallback copy method for text:', text);

    const textArea = document.createElement("textarea");
    textArea.value = text;

    // Avoid scrolling to bottom
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";
    textArea.style.opacity = "0";

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        const successful = document.execCommand('copy');
        console.log('execCommand copy result:', successful);

        if (successful) {
            console.log('Fallback copy successful');
            showCopyEventToast(successMessage);
            updateCopyButton(btn, true);
        } else {
            console.log('Fallback copy failed');
            showCopyEventToast('Failed to copy link - please copy manually: ' + text);
            updateCopyButton(btn, false);
        }
    } catch (err) {
        console.error('Fallback: Could not copy text: ', err);
        showCopyEventToast('Copy not supported - Link: ' + text);
        updateCopyButton(btn, false);
    }

    document.body.removeChild(textArea);
}

// Fallback function for older browsers (Events List)
function fallbackCopyTextToClipboardList(text, btn, successMessage) {
    console.log('Using fallback copy method for list with text:', text);

    const textArea = document.createElement("textarea");
    textArea.value = text;

    // Avoid scrolling to bottom
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";
    textArea.style.opacity = "0";

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        const successful = document.execCommand('copy');
        console.log('execCommand copy result for list:', successful);

        if (successful) {
            console.log('Fallback copy successful for list');
            showCopyEventToast(successMessage);
            updateCopyButtonList(btn, true);
        } else {
            console.log('Fallback copy failed for list');
            showCopyEventToast('Failed to copy link - please copy manually: ' + text);
            updateCopyButtonList(btn, false);
        }
    } catch (err) {
        console.error('Fallback: Could not copy text for list: ', err);
        showCopyEventToast('Copy not supported - Link: ' + text);
        updateCopyButtonList(btn, false);
    }

    document.body.removeChild(textArea);
}

// Helper function to update copy button appearance
function updateCopyButton(btn, success) {
    if (!btn) return;

    const $btn = $(btn);
    const originalHtml = $btn.html();

    if (success) {
        $btn.html('<i class="ti ti-check me-1"></i>Copied!')
            .removeClass('btn-outline-primary')
            .addClass('btn-success copy-link-dark-green')
            .prop('disabled', true);
    } else {
        $btn.html('<i class="ti ti-x me-1"></i>Failed')
            .removeClass('btn-outline-primary')
            .addClass('btn-danger')
            .prop('disabled', true);
    }

    // Reset button after 3 seconds
    setTimeout(function() {
        $btn.html(originalHtml)
            .removeClass('btn-success btn-danger copy-link-dark-green')
            .addClass('btn-outline-primary')
            .prop('disabled', false);
    }, 3000);
}

// Helper function to update copy button appearance for Events List
function updateCopyButtonList(btn, success) {
    if (!btn) return;

    const originalHtml = btn.innerHTML;
    const originalStyle = btn.style.background;

    if (success) {
        btn.innerHTML = '<i class="ti ti-check"></i>';
        btn.style.background = 'linear-gradient(to right, #198754, #20c997)';
        btn.disabled = true;
    } else {
        btn.innerHTML = '<i class="ti ti-x"></i>';
        btn.style.background = 'linear-gradient(to right, #dc3545, #fd7e14)';
        btn.disabled = true;
    }

    // Reset button after 3 seconds
    setTimeout(function() {
        btn.innerHTML = originalHtml;
        btn.style.background = originalStyle;
        btn.disabled = false;
    }, 3000);
}

// Handle meeting link click with validation
function handleMeetingLinkClick(event) {
    const link = event.target.closest('a').getAttribute('href');

    if (!link || link === '#' || link.trim() === '') {
        event.preventDefault();
        return false;
    }

    // Check if it's a phone number (starts with tel:)
    if (link.startsWith('tel:')) {
        // Allow default behavior for tel: links (will open phone app)
        return true;
    }

    // Validate if it's a proper URL
    try {
        new URL(link);
        // If URL is valid, allow the default behavior (open in new tab)
        return true;
    } catch (e) {
        event.preventDefault();
        return false;
    }
}

// Bookings Management Functions
function createNewBookingAction() {
    // Switch to calendar view and show a helpful message
    switchView('calendar');

    // Show a toast or alert to guide the user
    setTimeout(() => {
        showBookingsAlert('<?php echo e(__("To create a new booking, click on a date in the calendar and select an available time slot.")); ?>', 'info');
    }, 500);
}

function refreshBookings() {
    console.log('Refreshing bookings...');
    loadBookings();
}

// Global variable to track current appointment filter
let currentAppointmentFilter = 'upcoming'; // Default to upcoming
let allBookingsData = []; // Store all bookings for client-side filtering

function loadBookings() {
    console.log('Loading bookings...');

    // Show loading state
    $('#bookings-tbody').html(`
        <tr>
            <td colspan="<?php echo e(Auth::user() && Auth::user()->can('manage booking') ? '11' : '10'); ?>" class="text-center py-4">
                <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                <?php echo e(__('Loading bookings...')); ?>

            </td>
        </tr>
    `);

    // Get date filter values
    const startDate = $('#filter-start-date').val();
    const endDate = $('#filter-end-date').val();

    // Build URL with date filters (without appointment filter for now)
    let url = '<?php echo e(route("bookings.index")); ?>';
    const params = new URLSearchParams();
    if (startDate) params.append('start_date', startDate);
    if (endDate) params.append('end_date', endDate);
    if (params.toString()) url += '?' + params.toString();

    // Load all bookings and then filter client-side
    $.ajax({
        url: url,
        method: 'GET',
        success: function(response) {
            let tbody = $('#bookings-tbody');
            tbody.empty();

            try {
                // Parse the HTML response to extract booking data
                let $response = $(response);
                let $bookingRows = $response.find('#bookings-table tbody tr');

                if ($bookingRows.length > 0) {
                    // Check if it's the "no bookings" row
                    let firstRowText = $bookingRows.first().text().trim();
                    if (firstRowText.includes('No bookings found') || firstRowText.includes('<?php echo e(__("No bookings found")); ?>')) {
                        tbody.html(`
                            <tr>
                                <td colspan="<?php echo e(Auth::user() && Auth::user()->can('manage booking') ? '11' : '10'); ?>" class="text-center"><?php echo e(__('No bookings found')); ?></td>
                            </tr>
                        `);
                        allBookingsData = [];
                    } else {
                        // Store all booking rows for filtering
                        allBookingsData = [];
                        
                        $bookingRows.each(function(index) {
                            let $row = $(this);
                            let bookingId = extractBookingId($row);
                            let bookingDate = extractBookingDate($row);
                            
                            if (bookingId) {
                                // Add checkbox as first column
                                let checkboxHtml = `
                                    <td>
                                        <div class="form-check">
                                            <input class="form-check-input booking-checkbox" type="checkbox" value="${bookingId}" id="booking-${bookingId}">
                                            <label class="form-check-label" for="booking-${bookingId}"></label>
                                        </div>
                                    </td>
                                `;
                                $row.prepend(checkboxHtml);
                                
                                // Store booking data for filtering
                                allBookingsData.push({
                                    id: bookingId,
                                    date: bookingDate,
                                    row: $row
                                });
                            }
                        });
                        
                        // Apply current filter
                        applyAppointmentFilter();
                    }
                } else {
                    tbody.html(`
                        <tr>
                            <td colspan="<?php echo e(Auth::user() && Auth::user()->can('manage booking') ? '11' : '10'); ?>" class="text-center"><?php echo e(__('No bookings found')); ?></td>
                        </tr>
                    `);
                    allBookingsData = [];
                }
            } catch (error) {
                console.error('Error parsing bookings data:', error);
                tbody.html(`
                    <tr>
                        <td colspan="<?php echo e(Auth::user() && Auth::user()->can('manage booking') ? '11' : '10'); ?>" class="text-center">
                            <div class="alert alert-info">
                                <i class="ti ti-info-circle me-2"></i>
                                <?php echo e(__('Unable to load bookings dynamically. Please')); ?>

                                <a href="<?php echo e(route('bookings.index')); ?>" target="_blank" class="alert-link"><?php echo e(__('click here')); ?></a>
                                <?php echo e(__('to view bookings in a new tab.')); ?>

                            </div>
                        </td>
                    </tr>
                `);
            }
        },
        error: function(xhr) {
            console.error('Error loading bookings:', xhr);
            let tbody = $('#bookings-tbody');

            if (xhr.status === 403) {
                tbody.html(`
                    <tr>
                        <td colspan="<?php echo e(Auth::user() && Auth::user()->can('manage booking') ? '11' : '10'); ?>" class="text-center">
                            <div class="alert alert-warning">
                                <i class="ti ti-lock me-2"></i>
                                <?php echo e(__('You do not have permission to view bookings.')); ?>

                            </div>
                        </td>
                    </tr>
                `);
            } else {
                tbody.html(`
                    <tr>
                        <td colspan="<?php echo e(Auth::user() && Auth::user()->can('manage booking') ? '11' : '10'); ?>" class="text-center">
                            <div class="alert alert-danger">
                                <i class="ti ti-exclamation-triangle me-2"></i>
                                <?php echo e(__('Error loading bookings. Please')); ?>

                                <a href="<?php echo e(route('bookings.index')); ?>" target="_blank" class="alert-link"><?php echo e(__('click here')); ?></a>
                                <?php echo e(__('to view bookings in a new tab.')); ?>

                            </div>
                        </td>
                    </tr>
                `);
            }
        }
    });
}

// Function to filter appointments by type (upcoming/past/cancelled)
function filterAppointments(filterType) {
    console.log('Filtering appointments by:', filterType);
    
    // Update global filter state
    currentAppointmentFilter = filterType;
    
    // Update button states
    $('#upcoming-filter-btn, #past-filter-btn, #cancelled-filter-btn').removeClass('btn-primary').addClass('btn-outline-primary');
    
    if (filterType === 'upcoming') {
        $('#upcoming-filter-btn').removeClass('btn-outline-primary').addClass('btn-primary');
    } else if (filterType === 'past') {
        $('#past-filter-btn').removeClass('btn-outline-primary').addClass('btn-primary');
    } else if (filterType === 'cancelled') {
        $('#cancelled-filter-btn').removeClass('btn-outline-primary').addClass('btn-primary');
    }
    
    // Apply filter to existing data
    applyAppointmentFilter();
}

// Function to apply appointment filter to loaded data
function applyAppointmentFilter() {
    console.log('Applying appointment filter:', currentAppointmentFilter);
    
    if (allBookingsData.length === 0) {
        return;
    }
    
    const currentDate = new Date();
    currentDate.setHours(0, 0, 0, 0); // Set to start of day for comparison
    
    let filteredBookings = allBookingsData.filter(booking => {
        if (!booking.date) return true; // Include bookings without dates
        
        const bookingDate = new Date(booking.date);
        bookingDate.setHours(0, 0, 0, 0); // Set to start of day for comparison
        
        if (currentAppointmentFilter === 'upcoming') {
            return bookingDate >= currentDate && booking.status !== 'canceled';
        } else if (currentAppointmentFilter === 'past') {
            return bookingDate < currentDate && booking.status !== 'canceled';
        } else if (currentAppointmentFilter === 'cancelled') {
            return booking.status === 'canceled';
        }
        
        return true; // Default: show all
    });
    
    // Clear tbody and add filtered rows
    let tbody = $('#bookings-tbody');
    tbody.empty();
    
    if (filteredBookings.length > 0) {
        filteredBookings.forEach(booking => {
            tbody.append(booking.row);
        });
    } else {
        let filterText = '';
        if (currentAppointmentFilter === 'upcoming') {
            filterText = 'upcoming';
        } else if (currentAppointmentFilter === 'past') {
            filterText = 'past';
        } else if (currentAppointmentFilter === 'cancelled') {
            filterText = 'canceled';
        }
        tbody.html(`
            <tr>
                <td colspan="<?php echo e(Auth::user() && Auth::user()->can('manage booking') ? '11' : '10'); ?>" class="text-center">
                    <div class="alert alert-info">
                        <i class="ti ti-info-circle me-2"></i>
                        <?php echo e(__('No')); ?> ${filterText} <?php echo e(__('appointments found')); ?>

                    </div>
                </td>
            </tr>
        `);
    }
    
    console.log(`Filtered ${allBookingsData.length} bookings to ${filteredBookings.length} ${currentAppointmentFilter} appointments`);
}

// Helper function to extract booking date from row
function extractBookingDate($row) {
    // Try to find the date in the Date column (usually 8th column)
    let dateText = '';
    
    // Look for date in various possible positions
    const dateCells = $row.find('td');
    
    // Try to find a cell that contains a date pattern
    dateCells.each(function() {
        const cellText = $(this).text().trim();
        // Look for date patterns like YYYY-MM-DD, DD/MM/YYYY, MM/DD/YYYY, etc.
        if (cellText.match(/\d{4}-\d{2}-\d{2}/) ||
            cellText.match(/\d{1,2}\/\d{1,2}\/\d{4}/) ||
            cellText.match(/\d{1,2}-\d{1,2}-\d{4}/)) {
            dateText = cellText;
            return false; // Break the loop
        }
    });
    
    if (!dateText) {
        // If no date pattern found, try the 8th column (Date column)
        if (dateCells.length >= 8) {
            dateText = dateCells.eq(7).text().trim(); // 0-indexed, so 7 is the 8th column
        }
    }
    
    // Parse the date
    if (dateText) {
        try {
            // Try different date formats
            let parsedDate = null;
            
            if (dateText.match(/\d{4}-\d{2}-\d{2}/)) {
                // YYYY-MM-DD format
                parsedDate = new Date(dateText);
            } else if (dateText.match(/\d{1,2}\/\d{1,2}\/\d{4}/)) {
                // MM/DD/YYYY or DD/MM/YYYY format
                parsedDate = new Date(dateText);
            } else if (dateText.match(/\d{1,2}-\d{1,2}-\d{4}/)) {
                // DD-MM-YYYY format
                const parts = dateText.split('-');
                if (parts.length === 3) {
                    parsedDate = new Date(parts[2], parts[1] - 1, parts[0]);
                }
            }
            
            if (parsedDate && !isNaN(parsedDate.getTime())) {
                return parsedDate.toISOString().split('T')[0]; // Return YYYY-MM-DD format
            }
        } catch (e) {
            console.warn('Error parsing date:', dateText, e);
        }
    }
    
    return null;
}

// Helper function to extract booking ID from row
function extractBookingId($row) {
    // Try to find booking ID from various sources in the row
    let bookingId = null;
    
    // Look for data attributes
    if ($row.data('booking-id')) {
        bookingId = $row.data('booking-id');
    }
    // Look for ID in the first cell (ID column)
    else if ($row.find('td').first().text().trim().match(/^\d+$/)) {
        bookingId = $row.find('td').first().text().trim();
    }
    // Look for ID in action buttons
    else {
        let actionButtons = $row.find('button[onclick*="viewBooking"], button[onclick*="editBooking"], button[onclick*="deleteBooking"]');
        if (actionButtons.length > 0) {
            let onclickAttr = actionButtons.first().attr('onclick');
            let match = onclickAttr.match(/\d+/);
            if (match) {
                bookingId = match[0];
            }
        }
    }
    
    return bookingId;
}

// Bulk booking operations helper functions
function getSelectedBookingIds() {
    const selectedIds = [];
    $('.booking-checkbox:checked').each(function() {
        selectedIds.push(parseInt($(this).val()));
    });
    return selectedIds;
}

function updateBookingSelectAllState() {
    const totalCheckboxes = $('.booking-checkbox').length;
    const checkedCheckboxes = $('.booking-checkbox:checked').length;

    if (checkedCheckboxes === 0) {
        $('#select-all-bookings').prop('indeterminate', false).prop('checked', false);
    } else if (checkedCheckboxes === totalCheckboxes) {
        $('#select-all-bookings').prop('indeterminate', false).prop('checked', true);
    } else {
        $('#select-all-bookings').prop('indeterminate', true).prop('checked', false);
    }
}

function updateBulkBookingActionsVisibility() {
    const selectedCount = $('.booking-checkbox:checked').length;
    const bulkActions = $('#bulk-booking-actions');
    const selectedCountSpan = $('#selected-booking-count');

    if (selectedCount > 0) {
        selectedCountSpan.text(`${selectedCount} `);
        bulkActions.show();
    } else {
        bulkActions.hide();
    }
}

function bulkCancelBookings(bookingIds) {
    if (!bookingIds || bookingIds.length === 0) {
        showBookingsAlert('No appointments selected', 'warning');
        return;
    }

    // Show confirmation dialog
    if (!confirm(`Are you sure you want to cancel ${bookingIds.length} selected appointment(s)? This action cannot be undone.`)) {
        return;
    }

    // Show loading state
    $('#bulk-cancel-btn').prop('disabled', true).html('<i class="ti ti-loader me-1"></i><?php echo e(__("Cancelling...")); ?>');

    $.ajax({
        url: '<?php echo e(route("bookings.bulk-cancel")); ?>',
        method: 'POST',
        data: {
            booking_ids: bookingIds,
            _token: '<?php echo e(csrf_token()); ?>'
        },
        success: function(response) {
            if (response.success) {
                showBookingsAlert(response.message || 'Selected appointments cancelled successfully', 'success');
                // Reset selections and reload bookings
                $('.booking-checkbox').prop('checked', false);
                $('#select-all-bookings').prop('checked', false);
                updateBulkBookingActionsVisibility();
                loadBookings();
                
                // Refresh calendar if available
                if (calendar) {
                    calendar.refetchEvents();
                }
            } else {
                showBookingsAlert(response.message || 'Failed to cancel appointments', 'danger');
            }
        },
        error: function(xhr) {
            console.error('Error cancelling appointments:', xhr);
            let errorMessage = 'Failed to cancel appointments';
            
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                const errors = Object.values(xhr.responseJSON.errors).flat();
                errorMessage = errors.join(', ');
            }
            
            showBookingsAlert(errorMessage, 'danger');
        },
        complete: function() {
            // Re-enable button
            $('#bulk-cancel-btn').prop('disabled', false).html('<i class="ti ti-x me-1"></i><?php echo e(__("Bulk Cancel")); ?>');
        }
    });
}

// Date Filter Functions
function applyDateFilter() {
    const startDate = $('#filter-start-date').val();
    const endDate = $('#filter-end-date').val();

    if (!startDate && !endDate) {
        showBookingsAlert('<?php echo e(__("Please select at least one date to filter.")); ?>', 'warning');
        return;
    }

    if (startDate && endDate && startDate > endDate) {
        showBookingsAlert('<?php echo e(__("Start date cannot be after end date.")); ?>', 'warning');
        return;
    }

    loadBookings();
}

function clearDateFilter() {
    $('#filter-start-date').val('');
    $('#filter-end-date').val('');
    loadBookings();
}

// Status Management Functions
function changeBookingStatus(bookingId, newStatus) {
    if (!bookingId || !newStatus) {
        showBookingsAlert('<?php echo e(__("Invalid booking or status.")); ?>', 'error');
        return;
    }

    // Show loading state
    const $statusCell = $(`#booking-${bookingId} .status-dropdown`);
    const originalContent = $statusCell.html();
    $statusCell.html('<div class="spinner-border spinner-border-sm" role="status"></div>');

    $.ajax({
        url: `/bookings/${bookingId}/status`,
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        data: {
            status: newStatus
        },
        success: function(response) {
            if (response.success) {
                showBookingsAlert('<?php echo e(__("Booking status updated successfully.")); ?>', 'success');
                // Refresh the bookings to show updated status
                loadBookings();
            } else {
                showBookingsAlert('<?php echo e(__("Failed to update booking status.")); ?>', 'error');
                $statusCell.html(originalContent);
            }
        },
        error: function(xhr) {
            console.error('Error updating booking status:', xhr);
            let errorMessage = '<?php echo e(__("Failed to update booking status.")); ?>';
            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMessage = xhr.responseJSON.error;
            }
            showBookingsAlert(errorMessage, 'error');
            $statusCell.html(originalContent);
        }
    });
}

// Initialize default view on page load
$(document).ready(function() {
    // Set default active button styling
    switchView('calendar');

    // Handle per-page selector change
    $('#events-per-page').on('change', function() {
        const perPage = parseInt($(this).val());
        currentPerPage = perPage;
        loadEventsList(1, perPage); // Reset to first page when changing per-page
    });

    // Handle select all checkbox for events
    $('#select-all-events').on('change', function() {
        const isChecked = $(this).is(':checked');
        $('.event-checkbox').prop('checked', isChecked);
        updateBulkActionsVisibility();
    });

    // Handle individual checkbox changes for events
    $(document).on('change', '.event-checkbox', function() {
        updateSelectAllState();
        updateBulkActionsVisibility();
    });

    // Handle select all checkbox for bookings
    $('#select-all-bookings').on('change', function() {
        const isChecked = $(this).is(':checked');
        $('.booking-checkbox').prop('checked', isChecked);
        updateBulkBookingActionsVisibility();
    });

    // Handle individual checkbox changes for bookings
    $(document).on('change', '.booking-checkbox', function() {
        updateBookingSelectAllState();
        updateBulkBookingActionsVisibility();
    });

    // Handle bulk cancel button
    $('#bulk-cancel-btn').on('click', function() {
        const selectedIds = getSelectedBookingIds();
        if (selectedIds.length > 0) {
            bulkCancelBookings(selectedIds);
        }
    });

    // Handle bulk status change
    $('#bulk-status').on('change', function() {
        const status = $(this).val();
        if (status) {
            const selectedIds = getSelectedEventIds();
            if (selectedIds.length > 0) {
                bulkUpdateStatus(selectedIds, status);
            }
        }
    });

    // Handle bulk delete
    $('#bulk-delete-btn').on('click', function() {
        const selectedIds = getSelectedEventIds();
        if (selectedIds.length > 0) {
            bulkDeleteEvents(selectedIds);
        }
    });

    // Load bookings when bookings section is shown
    $(document).on('click', '#bookings-btn', function() {
        setTimeout(() => {
            if ($('#bookings-section').is(':visible')) {
                loadBookings();
            }
        }, 100);
    });

    // Also load bookings if the page loads with bookings section visible
    if ($('#bookings-section').is(':visible')) {
        loadBookings();
    }
});

// Bookings Helper Functions
function showCustomFieldsModal(customFieldsData, contactName) {
    let content = '<div class="row">';

    // Add contact name header
    content += `
        <div class="col-12 mb-4">
            <div class="alert alert-primary">
                <h6 class="mb-0">
                    <i class="ti ti-user me-2"></i>Custom Fields for: <strong>${contactName}</strong>
                </h6>
            </div>
        </div>
    `;

    if (customFieldsData && Array.isArray(customFieldsData) && customFieldsData.length > 0) {
        customFieldsData.forEach(function(field) {
            const icon = getFieldIcon(field.type);
            const formattedValue = formatFieldValue(field.type, field.value);

            content += `
                <div class="col-md-6 mb-3">
                    <div class="card custom-field-card h-100 border-primary">
                        <div class="card-body p-3">
                            <div class="d-flex align-items-start">
                                <div class="flex-shrink-0 me-3">
                                    <div class="custom-field-icon bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                        <i class="ti ti-${icon}"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-2 text-primary fw-bold">${field.label}</h6>
                                    <div class="field-value-container">
                                        <span class="badge bg-light text-dark fs-6 p-2 w-100 text-start">${formattedValue}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
    } else {
        content += '<div class="col-12"><div class="alert alert-warning text-center"><i class="ti ti-info-circle me-2"></i>No custom fields available for this booking</div></div>';
    }

    content += '</div>';

    $('#customFieldsContent').html(content);
    $('#customFieldsModal').modal('show');
}

function getFieldLabel(fieldKey) {
    const fieldLabels = {
        'contact_type': 'Contact Type',
        'date_of_birth': 'Date of Birth',
        'business_type': 'Business Type',
        'business_gst_number': 'Business GST Number',
        'lead_value': 'Lead Value',
        'assigned_to_staff': 'Assigned to Staff',
        'contact_source': 'Contact Source',
        'opportunity_name': 'Opportunity Name',
        'postal_code': 'Postal Code',
        'full_name': 'Full Name',
        'specific_requirement': 'Any Specific Requirement',
        'used_whatsapp_api_chatbots': 'Have you used WhatsApp API and Chatbots ever',
        'generate_leads': 'How do you generate leads',
        'hear_about_omx_sales': 'Where did you hear about OMX Sales?',
        'city': 'City',
        'have_msme_certificate': 'Do you have MSME Certificate?',
        'whatsapp_number': 'WhatsApp Number',
        'meta_business_name': 'META Business Name',
        'have_website': 'Do you have a website?',
        'business_industry': 'Business Industry',
        'message': 'Message',
        'organization_task': 'Organization Task',
        'team_size': 'Team Size',
        'company_revenue': 'Company Revenue',
        'budget': 'What is your budget?',
        'real_estate_services': 'What type of real estate services do you offer?',
        'using_chatbot_tools': 'Are you currently using any chatbot or automation tools?',
        'implement_chatbot_timeframe': 'How soon are you looking to implement a chatbot?',
        'running_digital_ads': 'Are you currently running any digital ads?',
        'monthly_advertising_budget': 'Monthly Advertising Budget',
        'promoted_projects_count': 'How many real estate projects are you promoting?',
        'biggest_marketing_challenge': 'What is your biggest marketing challenge?',
        'property_price_range': 'What is the price range of the property you are selling?',
        'using_crm_software': 'Are you currently using any CRM software?',
        'advertising_on_third_party_platforms': 'Are you advertising on any third-party platforms?',
        'know_whatsapp_api': 'Do you know about the WhatsApp API?',
        'messages_volume': 'How many messages do you need to send?',
        'using_whatsapp_official_api': 'How are you currently doing WhatsApp Official API?',
        'monthly_lead_sales_volume': 'Monthly Lead/Sales Volume'
    };

    return fieldLabels[fieldKey] || fieldKey.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}

function formatFieldValue(fieldKey, value) {
    // Format specific field types
    if (fieldKey === 'date_of_birth' && value) {
        const date = new Date(value);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }

    if ((fieldKey.includes('budget') || fieldKey.includes('revenue') || fieldKey.includes('value')) && value) {
        // Format currency values
        if (!isNaN(value)) {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD'
            }).format(value);
        }
    }

    if (fieldKey === 'team_size' && value) {
        return value + ' employees';
    }

    if ((fieldKey.includes('have_') || fieldKey.includes('using_') || fieldKey.includes('know_')) && value) {
        return value.charAt(0).toUpperCase() + value.slice(1);
    }

    return value || 'Not specified';
}

function getFieldIcon(fieldKey) {
    const fieldIcons = {
        'contact_type': 'phone',
        'date_of_birth': 'calendar',
        'business_type': 'building-store',
        'business_gst_number': 'file-text',
        'lead_value': 'currency-dollar',
        'assigned_to_staff': 'user',
        'contact_source': 'source',
        'opportunity_name': 'target',
        'postal_code': 'map-pin',
        'full_name': 'user-circle',
        'specific_requirement': 'list-details',
        'used_whatsapp_api_chatbots': 'brand-whatsapp',
        'generate_leads': 'trending-up',
        'hear_about_omx_sales': 'ear',
        'city': 'map-pin',
        'have_msme_certificate': 'certificate',
        'whatsapp_number': 'brand-whatsapp',
        'meta_business_name': 'brand-facebook',
        'have_website': 'world-www',
        'business_industry': 'building',
        'message': 'message',
        'organization_task': 'checklist',
        'team_size': 'users',
        'company_revenue': 'chart-line',
        'budget': 'wallet',
        'real_estate_services': 'home',
        'using_chatbot_tools': 'robot',
        'implement_chatbot_timeframe': 'clock',
        'running_digital_ads': 'ad',
        'monthly_advertising_budget': 'currency-dollar',
        'promoted_projects_count': 'hash',
        'biggest_marketing_challenge': 'alert-triangle',
        'property_price_range': 'home-dollar',
        'using_crm_software': 'database',
        'advertising_on_third_party_platforms': 'external-link',
        'know_whatsapp_api': 'brand-whatsapp',
        'messages_volume': 'message-circle',
        'using_whatsapp_official_api': 'api',
        'monthly_lead_sales_volume': 'chart-bar'
    };
    return fieldIcons[fieldKey] || 'info-circle';
}
// Alert system functions for bookings
function showBookingsAlert(message, type = 'success') {
    const alertContainer = $('#bookings-alert-container');
    const alertMessage = $('#bookings-alert-message');
    const alertText = $('#bookings-alert-text');
    // Set alert type and message
    alertMessage.removeClass('alert-success alert-danger alert-warning alert-info');
    alertMessage.addClass(`alert-${type}`);
    alertText.text(message);

    // Show alert
    alertContainer.show();

    // Auto-hide after 5 seconds
    setTimeout(function() {
        alertContainer.fadeOut();
    }, 5000);
}

// Admin CRUD Functions for Bookings
// View booking details
function viewBooking(bookingId) {
    $.ajax({
        url: `<?php echo e(url('bookings')); ?>/${bookingId}`,
        method: 'GET',
        success: function(response) {
            if (response.success) {
                const booking = response.data;
                let content = `
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="card border-primary">
                                <div class="card-body">
                                    <h6 class="card-title text-primary"><i class="ti ti-user me-2"></i>Customer Information</h6>
                                    <p><strong>Name:</strong> ${booking.name}</p>
                                    <p><strong>Email:</strong> ${booking.email}</p>
                                    <p><strong>Phone:</strong> ${booking.phone || 'N/A'}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card border-info">
                                <div class="card-body">
                                    <h6 class="card-title text-info"><i class="ti ti-calendar me-2"></i>Booking Information</h6>
                                    <p><strong>Event:</strong> ${booking.event ? booking.event.title : 'N/A'}</p>
                                    <p><strong>Date:</strong> ${booking.date}</p>
                                    <p><strong>Time:</strong> ${booking.time || 'N/A'}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // Add location information if available
                if (booking.selected_location) {
                    const location = booking.selected_location;
                    let locationDisplay = '';
                    let isClickable = false;
                    let clickAction = '';

                    if (location.type && location.value) {
                        switch(location.type) {
                            case 'zoom':
                                locationDisplay = `<i class="fas fa-video location-icon text-primary"></i>Zoom Meeting`;
                                isClickable = true;
                                clickAction = `onclick="window.open('${location.value}', '_blank')"`;
                                break;
                            case 'meet':
                                locationDisplay = `<i class="fab fa-google location-icon text-success"></i>Google Meet`;
                                isClickable = true;
                                clickAction = `onclick="window.open('${location.value}', '_blank')"`;
                                break;
                            case 'skype':
                                locationDisplay = `<i class="fab fa-skype location-icon text-info"></i>Skype`;
                                isClickable = true;
                                clickAction = `onclick="window.open('${location.value}', '_blank')"`;
                                break;
                            case 'phone':
                                locationDisplay = `<i class="fas fa-phone location-icon text-warning"></i>Phone: ${location.value}`;
                                isClickable = true;
                                clickAction = `onclick="window.open('tel:${location.value}', '_self')"`;
                                break;
                            case 'address':
                                locationDisplay = `<i class="fas fa-map-marker-alt location-icon text-danger"></i>${location.value}`;
                                isClickable = true;
                                clickAction = `onclick="window.open('https://maps.google.com/?q=${encodeURIComponent(location.value)}', '_blank')"`;
                                break;
                            case 'custom':
                                locationDisplay = `<i class="fas fa-map-marker-alt location-icon text-secondary"></i>${location.value}`;
                                break;
                            default:
                                locationDisplay = `<i class="fas fa-map-marker-alt location-icon text-secondary"></i>${location.display || location.value || 'Location specified'}`;
                        }
                    } else if (location.display) {
                        locationDisplay = `<i class="fas fa-map-marker-alt me-2 text-secondary"></i>${location.display}`;
                    }

                    if (locationDisplay) {
                        const cursorStyle = isClickable ? 'cursor: pointer;' : '';
                        const hoverStyle = isClickable ? 'text-decoration: underline;' : '';

                        content += `
                            <div class="row">
                                <div class="col-12 mb-3">
                                    <div class="card border-warning">
                                        <div class="card-body">
                                            <h6 class="card-title text-warning"><i class="ti ti-map-pin me-2"></i>Meeting Location</h6>
                                            <p class="mb-0">
                                                <span class="${isClickable ? 'clickable-location' : ''}"
                                                      ${isClickable ? clickAction : ''}>
                                                    ${locationDisplay}
                                                </span>
                                                ${isClickable ? '<span class="location-hint">(Click to open)</span>' : ''}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                    }
                }

                // Add assigned staff information if available
                if (booking.event && booking.event.assigned_staff) {
                    const staff = booking.event.assigned_staff;
                    const staffType = staff.type.charAt(0).toUpperCase() + staff.type.slice(1);

                    content += `
                        <div class="row">
                            <div class="col-12 mb-3">
                                <div class="card border-primary">
                                    <div class="card-body">
                                        <h6 class="card-title text-primary"><i class="ti ti-user-check me-2"></i>Assigned Staff</h6>
                                        <p class="mb-0">
                                            <span class="badge bg-primary me-2">
                                                <i class="ti ti-user me-1"></i>${staff.name}
                                            </span>
                                            <small class="text-muted">${staffType}</small>
                                        </p>
                                        ${staff.email ? `<small class="text-muted d-block mt-1"><i class="ti ti-mail me-1"></i>${staff.email}</small>` : ''}
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    content += `
                        <div class="row">
                            <div class="col-12 mb-3">
                                <div class="card border-light">
                                    <div class="card-body">
                                        <h6 class="card-title text-muted"><i class="ti ti-user-off me-2"></i>Assigned Staff</h6>
                                        <p class="mb-0">
                                            <span class="badge bg-light text-muted">
                                                <i class="ti ti-user-off me-1"></i>Not Assigned
                                            </span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                }

                // Continue with the rest of the content

                // Add custom fields if available
                if (booking.custom_fields && booking.custom_fields_value) {
                    content += `
                        <div class="row">
                            <div class="col-12">
                                <div class="card border-success">
                                    <div class="card-body">
                                        <h6 class="card-title text-success"><i class="ti ti-forms me-2"></i>Custom Fields</h6>
                                        <div class="row">
                    `;

                    // Process custom fields
                    const fields = Array.isArray(booking.custom_fields) ? booking.custom_fields : [];
                    const values = Array.isArray(booking.custom_fields_value) ? booking.custom_fields_value : [];

                    for(let i = 0; i < Math.min(fields.length, values.length); i++) {
                        const fieldType = fields[i];
                        const fieldValue = values[i];
                        const fieldLabel = getFieldLabel(fieldType);

                        content += `
                            <div class="col-md-6 mb-2">
                                <strong>${fieldLabel}:</strong> ${fieldValue || 'N/A'}
                            </div>
                        `;
                    }

                    content += `
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                }

                content += `
                    <div class="row">
                        <div class="col-12">
                            <div class="card border-secondary">
                                <div class="card-body">
                                    <h6 class="card-title text-secondary"><i class="ti ti-info-circle me-2"></i>System Information</h6>
                                    <p><strong>Booking ID:</strong> ${booking.id}</p>
                                    <p><strong>Created:</strong> ${new Date(booking.created_at).toLocaleString()}</p>
                                    <p><strong>Updated:</strong> ${new Date(booking.updated_at).toLocaleString()}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                $('#viewBookingContent').html(content);
                $('#viewBookingModal').modal('show');
            } else {
                showBookingsAlert('Error loading booking details: ' + response.message, 'danger');
            }
        },
        error: function(xhr) {
            showBookingsAlert('Error loading booking details', 'danger');
            console.error(xhr.responseText);
        }
    });
}

// Edit booking
function editBooking(bookingId) {
    // Load booking data
    $.ajax({
        url: `<?php echo e(url('bookings')); ?>/${bookingId}`,
        method: 'GET',
        success: function(response) {
            if (response.success) {
                const booking = response.data;

                // Populate form fields
                $('#edit_booking_id').val(booking.id);
                $('#edit_booking_name').val(booking.name);
                $('#edit_booking_email').val(booking.email);
                $('#edit_booking_phone').val(booking.phone || '');
                $('#edit_booking_date').val(booking.date);
                $('#edit_booking_time').val(booking.time || '');

                // Load events for dropdown
                loadEventsForEdit(booking.event_id);

                // Load custom fields
                loadCustomFieldsForEdit(booking);

                $('#editBookingModal').modal('show');
            } else {
                showBookingsAlert('Error loading booking details: ' + response.message, 'danger');
            }
        },
        error: function(xhr) {
            showBookingsAlert('Error loading booking details', 'danger');
            console.error(xhr.responseText);
        }
    });
}

// Load events for edit dropdown
function loadEventsForEdit(selectedEventId) {
    $.ajax({
        url: '<?php echo e(route("calendar-events.index")); ?>',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                let options = '<option value=""><?php echo e(__("Select Event")); ?></option>';
                response.data.forEach(function(event) {
                    const selected = event.id == selectedEventId ? 'selected' : '';
                    options += `<option value="${event.id}" ${selected}>${event.title}</option>`;
                });
                $('#edit_booking_event').html(options);
            }
        },
        error: function(xhr) {
            console.error('Error loading events:', xhr.responseText);
        }
    });
}

// Load custom fields for edit
function loadCustomFieldsForEdit(booking) {
    let customFieldsHtml = '';

    if (booking.custom_fields && booking.custom_fields_value) {
        const fields = Array.isArray(booking.custom_fields) ? booking.custom_fields : [];
        const values = Array.isArray(booking.custom_fields_value) ? booking.custom_fields_value : [];

        if (fields.length > 0) {
            customFieldsHtml += '<h6 class="mb-3"><i class="ti ti-forms me-2"></i><?php echo e(__("Custom Fields")); ?></h6>';

            for(let i = 0; i < Math.min(fields.length, values.length); i++) {
                const fieldType = fields[i];
                const fieldValue = values[i];
                const fieldLabel = getFieldLabel(fieldType);

                customFieldsHtml += `
                    <div class="mb-3">
                        <label for="edit_custom_field_${i}" class="form-label">${fieldLabel}</label>
                        <input type="text" class="form-control" id="edit_custom_field_${i}"
                               name="custom_fields_value[${i}]" value="${fieldValue || ''}"
                               data-field-type="${fieldType}">
                    </div>
                `;
            }
        }
    }

    $('#edit_custom_fields_container').html(customFieldsHtml);
}

// Delete booking
function deleteBooking(bookingId) {
    // Create a more user-friendly confirmation dialog
    const confirmDelete = confirm('<?php echo e(__("Are you sure you want to delete this booking?")); ?>\n\n<?php echo e(__("This action cannot be undone and will permanently remove all booking data.")); ?>');

    if (confirmDelete) {
        $.ajax({
            url: `<?php echo e(url('bookings')); ?>/${bookingId}`,
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    showBookingsAlert('<?php echo e(__("Booking deleted successfully")); ?>', 'success');
                    loadBookings(); // Refresh the bookings list
                } else {
                    showBookingsAlert('Error: ' + response.message, 'danger');
                }
            },
            error: function(xhr) {
                showBookingsAlert('Error deleting booking', 'danger');
                console.error(xhr.responseText);
            }
        });
    }
}

// Handle edit booking form submission
$(document).on('submit', '#editBookingForm', function(e) {
    e.preventDefault();

    const bookingId = $('#edit_booking_id').val();
    const formData = {
        name: $('#edit_booking_name').val(),
        email: $('#edit_booking_email').val(),
        phone: $('#edit_booking_phone').val(),
        date: $('#edit_booking_date').val(),
        time: $('#edit_booking_time').val(),
        custom_fields_value: []
    };

    // Collect custom fields values
    $('#edit_custom_fields_container input[name^="custom_fields_value"]').each(function() {
        formData.custom_fields_value.push($(this).val());
    });

    $.ajax({
        url: `<?php echo e(url('bookings')); ?>/${bookingId}`,
        method: 'PUT',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        data: formData,
        success: function(response) {
            if (response.success) {
                showBookingsAlert('<?php echo e(__("Booking updated successfully")); ?>', 'success');
                $('#editBookingModal').modal('hide');
                loadBookings(); // Refresh the bookings list
            } else {
                showBookingsAlert('Error: ' + response.message, 'danger');
            }
        },
        error: function(xhr) {
            showBookingsAlert('Error updating booking', 'danger');
            console.error(xhr.responseText);
        }
    });
});

</script>

<!-- Custom Fields Modal -->
<div class="modal fade" id="customFieldsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="ti ti-forms me-2"></i><?php echo e(__('Custom Fields Details')); ?>

                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-4" id="customFieldsContent">
                <!-- Custom fields will be displayed here -->
            </div>
            <div class="modal-footer bg-light">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="ti ti-x me-1"></i><?php echo e(__('Close')); ?>

                </button>
            </div>
        </div>
    </div>
</div>

<!-- View Booking Modal -->
<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage booking')): ?>
<div class="modal fade" id="viewBookingModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title">
                    <i class="ti ti-eye me-2"></i><?php echo e(__('Appointment Details')); ?>

                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="viewBookingContent">
                <!-- Booking details will be displayed here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="ti ti-x me-1"></i><?php echo e(__('Close')); ?>

                </button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Booking Modal -->
<div class="modal fade" id="editBookingModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-warning text-white">
                <h5 class="modal-title">
                    <i class="ti ti-edit me-2"></i><?php echo e(__('Edit Booking')); ?>

                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="editBookingForm">
                <div class="modal-body">
                    <input type="hidden" id="edit_booking_id">

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_booking_name" class="form-label"><?php echo e(__('Customer Name')); ?> *</label>
                            <input type="text" class="form-control" id="edit_booking_name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit_booking_email" class="form-label"><?php echo e(__('Email')); ?> *</label>
                            <input type="email" class="form-control" id="edit_booking_email" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_booking_phone" class="form-label"><?php echo e(__('Phone')); ?></label>
                            <input type="text" class="form-control" id="edit_booking_phone">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit_booking_event" class="form-label"><?php echo e(__('Event')); ?></label>
                            <select class="form-control" id="edit_booking_event" disabled>
                                <option value=""><?php echo e(__('Loading events...')); ?></option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_booking_date" class="form-label"><?php echo e(__('Date')); ?> *</label>
                            <input type="date" class="form-control" id="edit_booking_date" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit_booking_time" class="form-label"><?php echo e(__('Time')); ?></label>
                            <input type="time" class="form-control" id="edit_booking_time">
                        </div>
                    </div>

                    <div id="edit_custom_fields_container">
                        <!-- Custom fields will be populated here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="ti ti-x me-1"></i><?php echo e(__('Cancel')); ?>

                    </button>
                    <button type="submit" class="btn btn-warning">
                        <i class="ti ti-device-floppy me-1"></i><?php echo e(__('Update Booking')); ?>

                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php endif; ?>

<!-- Location Modals -->
<!-- Zoom Modal -->
<div class="modal fade" id="zoomLocationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="ti ti-video me-2 text-primary"></i><?php echo e(__('Edit Location')); ?>

                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <div class="d-flex align-items-center mb-3">
                        <i class="ti ti-video me-2 text-primary"></i>
                        <select class="form-select" id="zoom_type">
                            <option value="zoom"><?php echo e(__('Zoom')); ?></option>
                        </select>
                    </div>
                    <input type="url" class="form-control" id="zoom_link" placeholder="https://zoom.us/j/123456789">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('CANCEL')); ?></button>
                <button type="button" class="btn btn-success" onclick="saveLocation('zoom')"><?php echo e(__('UPDATE')); ?></button>
            </div>
        </div>
    </div>
</div>

<!-- In-Person Modal -->
<div class="modal fade" id="in_personLocationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="ti ti-map-pin me-2 text-success"></i><?php echo e(__('Edit Location')); ?>

                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <div class="d-flex align-items-center mb-3">
                        <i class="ti ti-map-pin me-2 text-success"></i>
                        <select class="form-select" id="in_person_type">
                            <option value="in_person"><?php echo e(__('In-person meeting')); ?></option>
                        </select>
                    </div>
                    <textarea class="form-control" id="in_person_address" rows="3" placeholder="<?php echo e(__('Enter the physical address or location details')); ?>"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('CANCEL')); ?></button>
                <button type="button" class="btn btn-success" onclick="saveLocation('in_person')"><?php echo e(__('UPDATE')); ?></button>
            </div>
        </div>
    </div>
</div>

<!-- Phone Modal -->
<div class="modal fade" id="phoneLocationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="ti ti-phone me-2 text-info"></i><?php echo e(__('Edit Location')); ?>

                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <div class="d-flex align-items-center mb-3">
                        <i class="ti ti-phone me-2 text-info"></i>
                        <select class="form-select" id="phone_type">
                            <option value="phone"><?php echo e(__('Phone call')); ?></option>
                        </select>
                    </div>
                    <input type="tel" class="form-control" id="phone_number" placeholder="<?php echo e(__('Enter phone number or call details')); ?>">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('CANCEL')); ?></button>
                <button type="button" class="btn btn-success" onclick="saveLocation('phone')"><?php echo e(__('UPDATE')); ?></button>
            </div>
        </div>
    </div>
</div>

<!-- Google Meet Modal -->
<div class="modal fade" id="meetLocationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="ti ti-brand-google me-2 text-warning"></i><?php echo e(__('Edit Location')); ?>

                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <div class="d-flex align-items-center mb-3">
                        <i class="ti ti-brand-google me-2 text-warning"></i>
                        <select class="form-select" id="meet_type">
                            <option value="meet"><?php echo e(__('Google Meet')); ?></option>
                        </select>
                    </div>
                    <input type="url" class="form-control" id="meet_link" placeholder="https://meet.google.com/abc-defg-hij">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('CANCEL')); ?></button>
                <button type="button" class="btn btn-success" onclick="saveLocation('meet')"><?php echo e(__('UPDATE')); ?></button>
            </div>
        </div>
    </div>
</div>

<!-- Skype Modal -->
<div class="modal fade" id="skypeLocationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="ti ti-brand-skype me-2 text-primary"></i><?php echo e(__('Edit Location')); ?>

                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <div class="d-flex align-items-center mb-3">
                        <i class="ti ti-brand-skype me-2 text-primary"></i>
                        <select class="form-select" id="skype_type">
                            <option value="skype"><?php echo e(__('Skype')); ?></option>
                        </select>
                    </div>
                    <input type="text" class="form-control" id="skype_link" placeholder="<?php echo e(__('Skype ID or meeting link')); ?>">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('CANCEL')); ?></button>
                <button type="button" class="btn btn-success" onclick="saveLocation('skype')"><?php echo e(__('UPDATE')); ?></button>
            </div>
        </div>
    </div>
</div>

<!-- Others Modal -->
<div class="modal fade" id="othersLocationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="ti ti-dots me-2 text-secondary"></i><?php echo e(__('Edit Location')); ?>

                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <div class="d-flex align-items-center mb-3">
                        <i class="ti ti-dots me-2 text-secondary"></i>
                        <select class="form-select" id="others_type">
                            <option value="others"><?php echo e(__('Others')); ?></option>
                        </select>
                    </div>
                    <textarea class="form-control" id="others_details" rows="3" placeholder="<?php echo e(__('Enter custom location details')); ?>"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('CANCEL')); ?></button>
                <button type="button" class="btn btn-success" onclick="saveLocation('others')"><?php echo e(__('UPDATE')); ?></button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\omx-new-saas\resources\views/tasks/calendar.blade.php ENDPATH**/ ?>