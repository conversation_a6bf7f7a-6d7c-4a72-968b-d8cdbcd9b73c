<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\CustomField;
use App\Models\CalendarEvent;
use App\Models\Booking;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class CustomFieldsIntegrationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user
        $this->user = User::factory()->create();
        $this->actingAs($this->user);
    }

    /** @test */
    public function it_can_create_custom_fields_for_calendar_events()
    {
        $customField = CustomField::create([
            'name' => 'Company Name',
            'type' => 'text',
            'module' => 'calendar_events',
            'unique_key' => 'company-name-test',
            'created_by' => $this->user->id,
            'is_required' => true,
        ]);

        $this->assertDatabaseHas('custom_fields', [
            'name' => 'Company Name',
            'type' => 'text',
            'module' => 'calendar_events',
            'unique_key' => 'company-name-test',
        ]);
    }

    /** @test */
    public function it_can_create_calendar_event_with_custom_fields()
    {
        // Create custom fields
        $customField1 = CustomField::create([
            'name' => 'Company Name',
            'type' => 'text',
            'module' => 'calendar_events',
            'unique_key' => 'company-name-test',
            'created_by' => $this->user->id,
            'is_required' => true,
        ]);

        $customField2 = CustomField::create([
            'name' => 'Budget Range',
            'type' => 'select',
            'module' => 'calendar_events',
            'unique_key' => 'budget-range-test',
            'options' => ['$1000-$5000', '$5000-$10000', '$10000+'],
            'created_by' => $this->user->id,
            'is_required' => false,
        ]);

        // Create calendar event with custom fields
        $eventData = [
            'title' => 'Test Event',
            'duration' => 60,
            'booking_per_slot' => 1,
            'minimum_notice' => 0,
            'description' => 'Test event description',
            'status' => 'active',
            'custom_fields' => [
                [
                    'unique_key' => 'company-name-test',
                    'type' => 'text',
                    'label' => 'Company Name',
                    'options' => [],
                    'is_required' => true
                ],
                [
                    'unique_key' => 'budget-range-test',
                    'type' => 'select',
                    'label' => 'Budget Range',
                    'options' => ['$1000-$5000', '$5000-$10000', '$10000+'],
                    'is_required' => false
                ]
            ]
        ];

        $response = $this->postJson(route('calendar-events.store'), $eventData);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        // Verify event was created with custom fields
        $this->assertDatabaseHas('calendar_events', [
            'title' => 'Test Event',
            'created_by' => $this->user->id,
        ]);

        $event = CalendarEvent::where('title', 'Test Event')->first();
        $this->assertNotNull($event->custom_fields);
        $this->assertCount(2, $event->custom_fields);
    }

    /** @test */
    public function it_displays_custom_fields_in_public_booking_form()
    {
        // Create custom field
        $customField = CustomField::create([
            'name' => 'Company Name',
            'type' => 'text',
            'module' => 'calendar_events',
            'unique_key' => 'company-name-test',
            'created_by' => $this->user->id,
            'is_required' => true,
        ]);

        // Create calendar event with custom fields
        $event = CalendarEvent::create([
            'title' => 'Test Event',
            'duration' => 60,
            'booking_per_slot' => 1,
            'minimum_notice' => 0,
            'description' => 'Test event description',
            'status' => 'active',
            'created_by' => $this->user->id,
            'custom_fields' => [
                [
                    'unique_key' => 'company-name-test',
                    'type' => 'text',
                    'label' => 'Company Name',
                    'options' => [],
                    'is_required' => true
                ]
            ]
        ]);

        // Access public booking form
        $response = $this->get(route('calendar-events.show', $event->id));

        $response->assertStatus(200);
        $response->assertSee('Company Name');
        $response->assertSee('custom_fields[company-name-test]');
    }

    /** @test */
    public function it_can_create_booking_with_custom_field_data()
    {
        // Create custom field
        $customField = CustomField::create([
            'name' => 'Company Name',
            'type' => 'text',
            'module' => 'calendar_events',
            'unique_key' => 'company-name-test',
            'created_by' => $this->user->id,
            'is_required' => true,
        ]);

        // Create calendar event with custom fields
        $event = CalendarEvent::create([
            'title' => 'Test Event',
            'duration' => 60,
            'booking_per_slot' => 1,
            'minimum_notice' => 0,
            'description' => 'Test event description',
            'status' => 'active',
            'created_by' => $this->user->id,
            'custom_fields' => [
                [
                    'unique_key' => 'company-name-test',
                    'type' => 'text',
                    'label' => 'Company Name',
                    'options' => [],
                    'is_required' => true
                ]
            ]
        ]);

        // Create booking with custom field data
        $bookingData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '1234567890',
            'event_id' => $event->id,
            'date' => now()->addDay()->format('Y-m-d'),
            'time' => '10:00',
            'custom_fields' => ['company-name-test'],
            'custom_fields_value' => ['Acme Corp'],
        ];

        $response = $this->postJson(route('public-bookings.store'), $bookingData);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        // Verify booking was created with custom field data
        $this->assertDatabaseHas('bookings', [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'event_id' => $event->id,
        ]);

        $booking = Booking::where('email', '<EMAIL>')->first();
        $this->assertEquals(['company-name-test'], $booking->custom_fields);
        $this->assertEquals(['Acme Corp'], $booking->custom_fields_value);
    }
}
